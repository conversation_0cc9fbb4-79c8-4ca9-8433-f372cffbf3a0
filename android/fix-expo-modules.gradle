// This file helps fix the conflict between com.android.application and com.android.library plugins
// when using Expo modules with React Native

ext.applyExpoModulesFixForApp = { Project project ->
    // Get all the Expo modules that need to be included
    def expoModules = []
    if (gradle.ext.has('expoAutolinkingManager')) {
        def manager = gradle.ext.expoAutolinkingManager
        def modules = manager.getModules(project.providers, true)

        for (module in modules) {
            for (moduleProject in module.projects) {
                // Skip the app project itself
                if (moduleProject.name == project.name) {
                    continue
                }

                expoModules.add(moduleProject.name)
            }
        }
    }

    // Add dependencies for all Expo modules
    project.dependencies {
        // Add expo-modules-core as a direct dependency
        if (rootProject.findProject(':expo-modules-core')) {
            implementation(rootProject.project(':expo-modules-core'))
        }

        // Add all other Expo modules
        for (moduleName in expoModules) {
            if (rootProject.findProject(":${moduleName}")) {
                implementation(rootProject.project(":${moduleName}"))
            }
        }
    }
}
