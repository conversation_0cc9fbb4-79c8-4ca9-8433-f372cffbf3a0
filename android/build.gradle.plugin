// This file is used to add the expo-module-gradle-plugin repository
buildscript {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.google.com' }
        maven { url 'https://jitpack.io' }
        maven { url 'https://repo.maven.apache.org/maven2' }
    }
    dependencies {
        // Add the expo-module-gradle-plugin dependency
        classpath 'host.exp.exponent:expo-module-gradle-plugin:0.0.1'
    }
}
