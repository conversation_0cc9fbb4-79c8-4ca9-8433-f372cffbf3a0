import React from 'react';
import { useNavigation, useTheme } from '@react-navigation/native';
import { View, Text, Image, TouchableOpacity, Platform } from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import LikeBtn from '../LikeBtn';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import { IMAGES } from '../../constants/Images';
import { useDispatch, useSelector } from 'react-redux';
import { removeFromwishList } from '../../redux/reducer/wishListReducer';

type Props = {
    id : string,
    title : string;
    text ?: string;
    price : string;
    image ?: any;
    btntitel?:any;
    removebtn?:any;
    discount?:any;
    grid?:any;
    review?:any;
    onPress ?:any,
    onPress1 ?: (e : any) => void,
    onPress2 ?: (e : any) => void,
}

const CardStyle3 = ({id, title, text, price, discount, image, btntitel, onPress, removebtn, grid, review,onPress1,onPress2 } : Props) => {

    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    const navigation = useNavigation<any>();

    const dispatch = useDispatch();

    const wishList = useSelector((state:any) => state.wishList.wishList);

    const inWishlist = () => {
        var temp = [] as any;
        wishList.forEach((data:any) => {
            temp.push(data.id);
        });
        return temp;
    }

    const removeItemFromWishList = () => {
        dispatch(removeFromwishList(id as any));
    }

    return (
        <TouchableOpacity
            activeOpacity={0.7}
            style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 10,
                borderBottomWidth: 1,
                borderBottomColor: colors.border,
                paddingBottom: 15,
                marginTop: 15,
                paddingHorizontal: 15
            }}
            onPress={() => navigation.navigate('ProductDetails')}
        >
            <View>
                <Image
                    style={{ height: undefined, width:Platform.OS === 'web' ? 250 : SIZES.width / 2.8 ,aspectRatio:1/1.2, borderRadius: 20 }}
                    source={image}
                />
            </View>
            <View style={{flex:1}}>
                <TouchableOpacity
                    onPress={() => navigation.navigate('ProductDetails')}
                >
                    <Text numberOfLines={1} style={{ ...FONTS.fontMedium, fontSize: 16, color: colors.title ,flex:1}}>{title}</Text>
                </TouchableOpacity>
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5, marginTop: 5 }}>
                    <Text style={{ ...FONTS.fontSemiBold, fontSize: 16, color: colors.title, }}>{price}</Text>
                    <Text
                        style={{
                            ...FONTS.fontRegular,
                            fontSize: 13,
                            textDecorationLine: 'line-through',
                            textDecorationColor: 'rgba(0, 0, 0, 0.70)',
                            color: theme.dark ? 'rgba(255,255,255,.7)' : 'rgba(0, 0, 0, 0.70)',
                            marginRight: 5
                        }}>{discount}
                    </Text>
                    {grid
                        ?
                        <Image
                            style={{ height: 12, width: 12, resizeMode: 'contain', }}
                            source={IMAGES.star4}
                        />
                        :
                        null
                    }
                    {grid
                        ?
                        <Text style={{ ...FONTS.fontRegular, fontSize: 12, color: theme.dark ? 'rgba(255,255,255,.5)' : 'rgba(0, 0, 0, 0.50)' }}>{review}</Text>
                        :
                        <Text style={{ ...FONTS.fontRegular, fontSize: 12, color: colors.title }}>Qty:<Text style={{ ...FONTS.fontRegular, fontSize: 14 }}>2</Text></Text>
                    }
                </View>
                <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: COLORS.success, marginTop: 5 }}>{text} Delivery</Text>
                <View style={{flexDirection:'row',alignItems:'center',justifyContent:'space-between'}}>
                    <Text style={{ ...FONTS.fontMedium, fontSize: 14, color: COLORS.danger, marginTop: 5 }}>40% Off</Text>
                    <View>
                        {removebtn ?
                            null
                            :
                            <TouchableOpacity
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    gap: 2,
                                    // position: 'absolute',
                                    // right: 20,
                                    // bottom: 30,
                                    backgroundColor: colors.title,
                                    borderRadius: 60,
                                    height: grid ? 40 : 32,
                                    width: grid ? 40 : 100,
                                    justifyContent: 'center'
                                }}
                                onPress={() => onPress && onPress()}
                            >
                                {grid
                                    ?
                                    <Image
                                        style={{ height: 18, width: 18, resizeMode: 'contain', tintColor: colors.card }}
                                        source={IMAGES.shopping}
                                    />
                                    :
                                    <Text style={{ ...FONTS.fontMedium, fontSize: 13, color: colors.card }}>{btntitel}</Text>
                                }
                            </TouchableOpacity>
                        }
                    </View>
                </View>
            </View>
            {grid ?
                <TouchableOpacity style={[GlobalStyleSheet.background, { position: 'absolute', top: 10, left: 25 }]}>
                    <LikeBtn
                        onPress={inWishlist().includes(id) ? removeItemFromWishList : onPress1}
                        id={id}
                        inWishlist={inWishlist} 
                    />
                </TouchableOpacity>
                :
                null
            }
        </TouchableOpacity>
    )
}

export default CardStyle3