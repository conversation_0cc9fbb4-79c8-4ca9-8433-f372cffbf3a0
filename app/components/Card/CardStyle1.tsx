import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import LikeBtn from '../LikeBtn';
import { FONTS, COLORS } from '../../constants/theme';
import { useNavigation, useTheme } from '@react-navigation/native';
import Button from '../Button/Button';
import { IMAGES } from '../../constants/Images';
import { useDispatch, useSelector } from 'react-redux';
import { removeFromwishList } from '../../redux/reducer/wishListReducer';

type Props = {
    id : string,
    title : string;
    price : string;
    image ?: any;
    closebtn?:any;
    review?:any;
    discount?:any;
    likebtn?:any;
    onPress ?:any,
    onPress1 ?: (e : any) => void,
    onPress2 ?: (e : any) => void,
}

const CardStyle1 = ({id, image, title, price, discount, review, closebtn, onPress, likebtn,onPress1,onPress2} : Props) => {

    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    const navigation = useNavigation<any>();

    const dispatch = useDispatch();

    const wishList = useSelector((state:any) => state.wishList.wishList);

    const inWishlist = () => {
        var temp = [] as any;
        wishList.forEach((data:any) => {
            temp.push(data.id);
        });
        return temp;
    }

    const removeItemFromWishList = () => {
        dispatch(removeFromwishList(id as any));
    }

    return (
        <TouchableOpacity
            activeOpacity={0.7}
            style={{}}
            onPress={() => onPress && onPress()}
        >
            <Image
                style={{ height:null, width: '100%', aspectRatio: 1 / 1.2, borderRadius: 20 }}
                source={image}
            />
            <View style={{ marginTop: 10 }}>
                <Text style={{ ...FONTS.fontMedium, fontSize: 14, color: colors.title }}>{title}</Text>
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5, marginTop: 5 }}>
                    <Text style={{ ...FONTS.fontSemiBold, fontSize: 16, color: colors.title, }}>{price}</Text>
                    <Text
                        style={{
                            ...FONTS.fontRegular,
                            fontSize: 13,
                            textDecorationLine: 'line-through',
                            textDecorationColor: theme.dark ? 'rgba(255,255,255, .7)' : 'rgba(0, 0, 0, 0.70)',
                            color: theme.dark ? 'rgba(255,255,255, .7)' : 'rgba(0, 0, 0, 0.70)',
                            marginRight: 5
                        }}>{discount}
                    </Text>
                    <Image
                        style={{ height: 12, width: 12, resizeMode: 'contain', }}
                        source={IMAGES.star4}
                    />
                    <Text style={{ ...FONTS.fontRegular, fontSize: 12, color: theme.dark ? 'rgba(255,255,255, .5)' : 'rgba(0, 0, 0, 0.50)' }}>{review}</Text>
                </View>
            </View>
            {likebtn
                ?
                <View style={{ position: 'absolute', right: 15, top: 10 }}>
                    <TouchableOpacity
                        onPress={onPress1}
                        activeOpacity={0.8}
                        style={{
                            height: 38,
                            width: 38,
                            borderRadius: 38,
                            backgroundColor: 'rgba(0,0,0,.2)',
                            alignItems: 'center',
                            justifyContent: 'center',
                        }}
                    >
                        <Image
                            style={{ height: 18, width: 18, resizeMode: 'contain', tintColor: COLORS.white }}
                            source={IMAGES.close}
                        />
                    </TouchableOpacity>
                </View>
                :
                <View style={{ position: 'absolute', right: 15, top: 10 }}>
                    <TouchableOpacity
                        style={{
                            height: 38,
                            width: 38,
                            borderRadius: 38,
                            backgroundColor: 'rgba(0,0,0,.2)',
                            alignItems: 'center',
                            justifyContent: 'center',
                        }}
                    >
                         <LikeBtn
                            onPress={inWishlist().includes(id) ? removeItemFromWishList : onPress1}
                            id={id}
                            inWishlist={inWishlist}
                        />
                    </TouchableOpacity>
                </View>
            }
            {closebtn ?
                <TouchableOpacity
                    style={{ position: 'absolute', width: '100%', bottom: 78 }}>
                    <Button
                        color={theme.dark ? 'rgba(255,255,255,.6)' : 'rgba(0, 0, 0, 0.60)'}
                        btnRounded
                        text={colors.card}
                        title={'Add To Cart'}
                        size={"sm"}
                        onPress={onPress2}
                    />
                </TouchableOpacity>
                :
                null
            }
        </TouchableOpacity>
    )
}

export default CardStyle1;