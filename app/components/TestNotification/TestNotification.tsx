import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import { COLORS, FONTS } from '../../constants/theme';

const TestNotification = () => {
  const [fcmToken, setFcmToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string | null>(null);

  useEffect(() => {
    // Get the FCM token when component mounts
    const getToken = async () => {
      try {
        const token = await AsyncStorage.getItem('fcmToken');
        if (token) {
          setFcmToken(token);
        } else {
          // If token not in AsyncStorage, get it directly from Firebase
          const newToken = await messaging().getToken();
          setFcmToken(newToken);
          await AsyncStorage.setItem('fcmToken', newToken);
        }
      } catch (error) {
        console.error('Error getting FCM token:', error);
        setResult('Error getting FCM token: ' + error.message);
      }
    };

    getToken();
  }, []);

  const sendLocalNotification = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      // This will trigger a foreground notification through our handler
      const message = {
        notification: {
          title: 'Test Local Notification',
          body: 'This is a test notification sent locally from your app',
        },
        data: {
          type: 'test',
          timestamp: Date.now().toString(),
        },
        messageId: 'test-' + Date.now(),
      };

      // Log the message for debugging
      console.log('Sending local test notification:', message);
      
      // Manually trigger the onMessage handler
      await messaging().onMessage(async (remoteMessage) => {
        console.log('Foreground notification received:', remoteMessage);
      })(message);
      
      setResult('Local notification sent successfully! Check if you received it.');
    } catch (error) {
      console.error('Error sending local notification:', error);
      setResult('Error sending local notification: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const checkPermissions = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      
      if (enabled) {
        setResult('Notification permissions enabled! Status: ' + authStatus);
      } else {
        setResult('Notification permissions denied! Status: ' + authStatus);
      }
    } catch (error) {
      console.error('Error checking permissions:', error);
      setResult('Error checking permissions: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const refreshToken = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      // Force refresh the FCM token
      const newToken = await messaging().getToken();
      await AsyncStorage.setItem('fcmToken', newToken);
      setFcmToken(newToken);
      setResult('FCM token refreshed successfully!');
    } catch (error) {
      console.error('Error refreshing token:', error);
      setResult('Error refreshing token: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Push Notification Tester</Text>
      
      <View style={styles.tokenContainer}>
        <Text style={styles.tokenLabel}>Your FCM Token:</Text>
        <Text style={styles.tokenText} selectable>{fcmToken || 'Loading...'}</Text>
        <Text style={styles.hint}>Long press to copy this token</Text>
      </View>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.button} 
          onPress={sendLocalNotification}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <Text style={styles.buttonText}>Test Local Notification</Text>
          )}
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={checkPermissions}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Check Permissions</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={refreshToken}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Refresh FCM Token</Text>
        </TouchableOpacity>
      </View>
      
      {result && (
        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Result:</Text>
          <Text style={styles.resultText}>{result}</Text>
        </View>
      )}
      
      <View style={styles.instructionsContainer}>
        <Text style={styles.instructionsTitle}>Troubleshooting Steps:</Text>
        <Text style={styles.instructionsText}>
          1. Check if you have notification permissions enabled{'\n'}
          2. Copy your FCM token and use it in Firebase Console{'\n'}
          3. Make sure your app is in the background when testing{'\n'}
          4. Try refreshing your FCM token if notifications aren't working{'\n'}
          5. Check the app console logs for any errors
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    margin: 10,
  },
  title: {
    ...FONTS.fontBold,
    fontSize: 20,
    color: COLORS.primary,
    marginBottom: 15,
    textAlign: 'center',
  },
  tokenContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  tokenLabel: {
    ...FONTS.fontMedium,
    fontSize: 16,
    marginBottom: 5,
    color: '#333',
  },
  tokenText: {
    ...FONTS.fontRegular,
    fontSize: 14,
    color: '#666',
    padding: 10,
    backgroundColor: '#f9f9f9',
    borderRadius: 5,
    overflow: 'hidden',
  },
  hint: {
    ...FONTS.fontRegular,
    fontSize: 12,
    color: '#999',
    marginTop: 5,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 10,
    marginBottom: 20,
  },
  button: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    ...FONTS.fontMedium,
    color: '#fff',
    fontSize: 16,
  },
  resultContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  resultTitle: {
    ...FONTS.fontMedium,
    fontSize: 16,
    marginBottom: 5,
    color: '#333',
  },
  resultText: {
    ...FONTS.fontRegular,
    fontSize: 14,
    color: '#666',
  },
  instructionsContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  instructionsTitle: {
    ...FONTS.fontMedium,
    fontSize: 16,
    marginBottom: 5,
    color: '#333',
  },
  instructionsText: {
    ...FONTS.fontRegular,
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
  },
});

export default TestNotification;
