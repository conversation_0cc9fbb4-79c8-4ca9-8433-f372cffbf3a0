import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  Alert,
} from "react-native";
import React, { useCallback, useRef, useState } from "react";
import { COLORS, FONTS, FONTWEIGHT, SIZES } from "../../constants/theme";
import Badge from "../Badge/Badge";
import BottomNavModal from "../BottomSheetNew/BottomSheetNew";
import { IMAGES } from "../../constants/Images";
import CartIcon from "../../assets/icons/addtocart.png";
import { useMutation } from "@apollo/client";
import { ADD_LINES_TO_CART, CREATE_CART } from "../../api/cartQuery";
import { useDispatch, useSelector } from "react-redux";
import { setCartId } from "../../redux/reducer/cartReducer";
type Props = {
  navigation?: any;
  data: any;
  badge?: any;
  handle?: any;
};

export default function SingleProductCard({
  navigation,
  data,
  badge,
  handle,
}: Props) {
  const [modalVisible, setModalVisible] = useState(false);

  const [activeIndex, setActiveIndex] = useState(0);
  const scrollRef = useRef(null);
  const [createCart, { loading, error }] = useMutation(CREATE_CART);
  const [addCart, { loading: addLoading }] = useMutation(ADD_LINES_TO_CART);
  const dispatch = useDispatch();
  const { cartId } = useSelector((state: any) => state.cart);
  const [selectedVariantId, setSelectedVariantId] = useState(
    data.variants[0].id
  );
  const [selectedSize, setSelectedSize] = useState(data?.sizes?.[0] || null);
  const [quantity, setQuanitity] = useState(1);

  const [currentProductDetails, setCurrentProductDetails] = useState(null);
  const openBottomSheet = useCallback(
    (product: any) => {
      setModalVisible(true);
      setCurrentProductDetails(product);
    },
    [modalVisible, currentProductDetails]
  );
  const varientsColor = data?.variants?.map((item: any, index: number) => {
    return item.title.toLowerCase();
  });

  const closeBottomSheet = () => {
    setModalVisible(false);
    setSelectedSize(null);
    setActiveIndex(0);
  };

  const handleAddToBag = () => {
    if (!data?.sizes?.length) {
      // If no sizes are available, proceed without size selection
      const lines = [
        {
          quantity,
          merchandiseId: selectedVariantId,
          attributes: [],
        },
      ];

      if (cartId) {
        addCart({
          variables: {
            cartId,
            lines: lines,
          },
        })
          .then((response: any) => {
            if (response.data.cartLinesAdd.userErrors.length > 0) {
              alert("Error: " + response.data.cartLinesAdd.userErrors[0].message);
            } else {
              Alert.alert("Success", "Added to cart successfully!", [
                {
                  text: "OK",
                  onPress: () => {
                    const cartId = response.data.cartLinesAdd.cart.id;
                    dispatch(setCartId(cartId));
                    navigation.push("MyCart", {
                      cartId,
                      lines,
                    });
                    closeBottomSheet();
                  },
                },
              ]);
            }
          })
          .catch((err) => {
            alert("Something went wrong. Please try again.");
            console.error("Cart addition error:", err);
          });
      } else {
        createCart({
          variables: {
            lines,
          },
        })
          .then((response: any) => {
            if (response.data.cartCreate.userErrors.length > 0) {
              alert("Error: " + response.data.cartCreate.userErrors[0].message);
            } else {
              Alert.alert("Success", "Added to cart successfully!", [
                {
                  text: "OK",
                  onPress: () => {
                    const cartId = response.data.cartCreate.cart.id;
                    dispatch(setCartId(cartId));
                    navigation.push("MyCart", {
                      cartId,
                      lines,
                    });
                    closeBottomSheet();
                  },
                },
              ]);
            }
          })
          .catch((err) => {
            alert("Something went wrong. Please try again.");
            console.error("Cart creation error:", err);
          });
      }
      return;
    }

    if (!selectedSize) {
      Alert.alert("Error", "Please select a size");
      return;
    }

    const lines = [
      {
        quantity,
        merchandiseId: selectedVariantId,
        attributes: [
          {
            key: "Size",
            value: selectedSize,
          },
        ],
      },
    ];

    if (cartId) {
      addCart({
        variables: {
          cartId,
          lines: lines,
        },
      })
        .then((response: any) => {
          if (response.data.cartLinesAdd.userErrors.length > 0) {
            alert("Error: " + response.data.cartLinesAdd.userErrors[0].message);
          } else {
            Alert.alert("Success", "Added to cart successfully!", [
              {
                text: "OK",
                onPress: () => {
                  const cartId = response.data.cartLinesAdd.cart.id;
                  // console.log("Cart Id: ", cartId);

                  dispatch(setCartId(cartId));

                  navigation.push("MyCart", {
                    cartId,
                    lines,
                  });
                  closeBottomSheet();
                },
              },
            ]);
          }
        })
        .catch((err) => {
          alert("Something went wrong. Please try again.");
          console.error("Cart addition error:", err);
        });
    } else {
      createCart({
        variables: {
          lines,
        },
      })
        .then((response: any) => {
          if (response.data.cartCreate.userErrors.length > 0) {
            alert("Error: " + response.data.cartCreate.userErrors[0].message);
          } else {
            Alert.alert("Success", "Added to cart successfully!", [
              {
                text: "OK",
                onPress: () => {
                  const cartId = response.data.cartCreate.cart.id;
                  // console.log("Cart Id: ", cartId);
                  dispatch(setCartId(cartId));
                  navigation.push("MyCart", {
                    cartId,
                    lines,
                  });
                  closeBottomSheet();
                },
              },
            ]);
            // closeBottomSheet()
          }
        })
        .catch((err) => {
          alert("Something went wrong. Please try again.");
          console.error("Cart creation error:", err);
        });
    }
  };

  const handleIncrease = () => {
    setQuanitity((pre) => pre + 1);
  };

  const handleDecrease = () => {
    setQuanitity((pre) => {
      if (pre <= 1) {
        return pre;
      }
      return pre - 1;
    });
  };

  const handleVariantChange = (variantId: string) => {
    if (variantId !== selectedVariantId) {
      setSelectedVariantId(variantId);
    }
  };

  const handleSizeChange = (size: string, index: number) => {
    setSelectedSize(size);
    setActiveIndex(index);
  };

  return (
    <>
      <View style={[{}]}>
        <Badge
          title="Pre order"
          color={COLORS.badgeBackgroundColor}
          size="md"
          style={{
            position: "absolute",
            zIndex: 10000,
            borderRadius: 8,
            marginTop: 5,
            marginHorizontal: 5,
          }}
        />
        <TouchableOpacity
          activeOpacity={0.7}
          style={{
            borderWidth: 1,
            borderColor: "white",
            borderRadius: 15,
            backgroundColor: COLORS.card,
          }}
          onPress={() => {
            navigation.navigate("ProductDetailsPage", {
              handle: data?.handle,
            });
          }}
        >
          <TouchableOpacity
            onPress={() => openBottomSheet(data)}
            style={{
              position: "absolute",
              bottom: 110,
              // top: 148,
              right: 0,
              backgroundColor: COLORS.addToCartBg,
              borderTopLeftRadius: 8,
              zIndex: 100000,
            }}
          >
            <Image
              source={CartIcon}
              style={{
                width: 40,
                height: 40,
              }}
            />
          </TouchableOpacity>
          {data?.image && (
            <Image
              style={{
                width: "100%",
                height: 188,
                aspectRatio: 2 / 2,
                borderTopLeftRadius: 15,
                borderTopRightRadius: 15,
              }}
              source={{ uri: data?.image }}
            />
          )}

          <View
            style={{
              flexDirection: "column",
              justifyContent: "space-between",
              marginVertical: 5,
              marginHorizontal: 8,
              marginTop: 3,
              width: 130,
              height: 100,
            }}
          >
            <Text
              style={{
                ...FONTS.fontRegular,
                fontSize: SIZES.fontXs,
                color: COLORS.textBrandName,
                ...FONTS.fontRegular,
              }}
            >
              {data?.vendor}
            </Text>

            <Text
              style={{
                width: "auto",
                ...FONTWEIGHT.SemiBold,
                ...FONTS.fontRegular,
                fontSize: SIZES.font,
                fontFamily: "DMSansSemiBold",
              }}
              numberOfLines={2}
              ellipsizeMode="tail"
            >
              {data?.title}
            </Text>

            <View style={{ flexDirection: "row", gap: 5 }}>
              <Text
                style={{
                  marginVertical: 5,
                  fontSize: SIZES.font,
                  ...FONTWEIGHT.SemiBold,
                  fontFamily: "DMSansSemiBold",
                }}
              >
                ${data?.price}
              </Text>
              {/* <Text
                      style={{
                        marginVertical: 5,
                        color: COLORS.darkgray,
                        fontSize: SIZES.font,
                        ...FONTWEIGHT.Light,
                        textDecorationLine: "line-through",
                        marginHorizontal: 2,
                        fontFamily: "DMSansMedium",
                      }}
                    >
                      $126.00
                    </Text> */}
            </View>
            <View
              style={{
                flexDirection: "row",
                gap: 10,
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  gap: 4,
                  alignItems: "center",
                }}
              >
                {varientsColor?.slice(0, 3)?.map((varient: any) => {
                  return (
                    <>
                      <View
                        key={varient}
                        style={{
                          width: 15,
                          height: 15,
                          backgroundColor: `${varient}`,
                          borderRadius: 25,
                        }}
                      ></View>
                    </>
                  );
                })}
              </View>
              {varientsColor?.length > 3 && (
                <Text style={{ fontFamily: "DMSansLight" }}>
                  +{varientsColor?.slice(3)?.length}
                </Text>
              )}
            </View>
          </View>
        </TouchableOpacity>
      </View>
      <BottomNavModal
        modalVisible={modalVisible}
        setModalVisible={setModalVisible}
        currentProductDetails={currentProductDetails}
        buttonWidth={190}
        clearAllBtn={true}
        leftIconWidth={140}
        buttonTitle={quantity}
        onQuantityDecrease={handleDecrease}
        onQuantityIncrease={handleIncrease}
        onPressRightBtn={handleAddToBag}
        rightIcon="+"
        leftIcon="-"
        headerEnabled={false}
        modelTitleTextComponent={
          <Text
            style={{
              marginTop: 5,
              marginBottom: 10,
              marginHorizontal: 10,
              fontSize: SIZES.fontLg,
              color: COLORS.title,
              fontFamily: "DMSansSemiBold",
            }}
          >
            {currentProductDetails?.title}
          </Text>
        }
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{}}
        >
          <View>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginBottom: 16,
              }}
            >
              <Text
                style={{
                  fontFamily: "DMSansBold",
                  fontSize: SIZES.font,
                  marginHorizontal: 10,
                }}
              >
                Choose Color
              </Text>
              <Text
                style={{ fontWeight: "400" }}
              >{`(${data?.variants?.length} Colors)`}</Text>
            </View>

            <View
              style={{
                marginHorizontal: 5,
              }}
            >
              <ScrollView
                horizontal
                // ref={scrollRef}
                showsHorizontalScrollIndicator={false}
                scrollEventThrottle={16}
              >
                {data?.variants.map((variant: any, index: any) => {
                  return (
                    <TouchableOpacity
                      onPress={() => {
                        handleVariantChange(variant.id);
                      }}
                      key={index}
                      style={{
                        alignItems: "center",
                        borderWidth: variant.id === selectedVariantId ? 1 : 0,
                        borderRadius: 10,
                        padding: 10,
                      }}
                    >
                      <Image
                        source={{ uri: variant?.image }}
                        style={{
                          width: 150,
                          height: undefined,
                          aspectRatio: 1 / 1,
                          objectFit: "cover",
                          borderRadius: 10,
                        }}
                      />
                      <Text
                        style={{
                          marginTop: 8,
                          width: "100%",
                          ...FONTS.font,
                          ...FONTS.fontRegular,
                        }}
                      >
                        {variant?.title}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
            </View>
          </View>
          {data?.sizes?.length > 0 && (
            <View style={{ marginTop: 20, marginHorizontal: 10 }}>
              <View>
                <Text
                  style={{ fontFamily: "DMSansBold", fontSize: SIZES.font }}
                >
                  Choose Size
                </Text>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  gap: 8,
                  flexWrap: "wrap",
                  marginTop: 16,
                }}
              >
                {data?.sizes?.map((size: any, index: any) => {
                  const cross = false;
                  return (
                    <TouchableOpacity
                      onPress={() => handleSizeChange(size, index)}
                      style={{
                        marginTop: 8,
                        borderWidth: 1,
                        borderRadius: 10,
                        paddingVertical: 10,
                        width: 76,
                        opacity: cross ? 0.2 : 1,
                        backgroundColor:
                          activeIndex === index ? COLORS.black : COLORS.white,
                      }}
                      disabled={cross}
                    >
                      <Text
                        style={{
                          textAlign: "center",
                          color:
                            activeIndex === index ? COLORS.white : COLORS.black,
                        }}
                      >
                        {size}
                      </Text>
                      {cross && (
                        <View
                          style={{
                            position: "absolute",
                            transform: [{ rotateZ: "155deg" }],
                            bottom: 20,
                            right: 0,
                            height: 0,
                            width: 82,
                            borderWidth: 0.3,
                          }}
                        ></View>
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>
          )}
        </ScrollView>
      </BottomNavModal>
    </>
  );
}
