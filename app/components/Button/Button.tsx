import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { COLORS, FONTS, SIZES } from "../../constants/theme";
import { useTheme } from "@react-navigation/native";
import { ActivityIndicator } from "react-native-paper";

type Props = {
  title: string;
  onPress?: any;
  onQuantityDecrease?: () => void;
  onQuantityIncrease?: () => void;
  color?: any;
  style?: object;
  size?: any;
  text?: string;
  btnRounded?: any;
  badge?: any;
  icon?: any;
  fullWidth?: any;
  outline?: any;
  height?: any;
  leftIcon?: any;
  rightIcon?: any;
  rightDisabled?: boolean;
  loading?: boolean;
};

const Button = ({
  title,
  color,
  onPress,
  style,
  size,
  badge,
  btnRounded,
  text,
  icon,
  fullWidth,
  outline,
  height,
  leftIcon = false,
  rightIcon = false,
  onQuantityDecrease = () => {},
  onQuantityIncrease = () => {},
  rightDisabled = false,
  loading = false,
}: Props) => {
  const theme = useTheme();
  const { colors }: { colors: any } = theme;

  return (
    <TouchableOpacity
      disabled={rightDisabled||loading}
      activeOpacity={0.8}
      onPress={() => onPress && onPress()}
      style={[
        {
          height: outline ? 48 : 55,
          paddingHorizontal: 25,
          paddingVertical: 13,
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          borderRadius: btnRounded ? 30 : SIZES.radius,
          backgroundColor: color
            ? color
            : outline
            ? colors.card
            : COLORS.primary,
          borderWidth: outline ? 1 : null,
          borderColor: theme.dark
            ? COLORS.white
            : outline
            ? colors.borderColor
            : null,
        },
        size === "sm" && {
          paddingHorizontal: 25,
          paddingVertical: 10,
          height: height ? 46 : 40,
        },
        size === "lg" && {
          paddingHorizontal: 35,
          paddingVertical: 16,
          height: 58,
        },
        icon && {
          paddingLeft: 65,
          paddingRight: fullWidth ? 65 : 25,
        },
        style && { ...style },
      ]}
    >
      {icon && (
        <View
          style={{
            height: outline ? 48 : 55,
            width: outline ? 48 : 55,
            borderRadius: 55,
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: outline
              ? colors.title
              : theme.dark
              ? COLORS.title
              : COLORS.white,
            borderWidth: outline ? 0 : 2,
            borderColor: color ? color : outline ? "" : COLORS.primary,
            position: "absolute",
            left: 0,
          }}
        >
          {icon}
        </View>
      )}
      {loading ? (
        <View
          style={{
            position: "absolute",
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <ActivityIndicator size="small" color={COLORS.white} />
        </View>
      ) : (
        <Text
          style={[
            {
              ...FONTS.fontLg,
              textAlign: outline ? null : "center",
              flexDirectoin: "row",
              color: text
                ? text
                : outline
                ? colors.title
                : theme.dark
                ? COLORS.title
                : COLORS.white,
            },
            size === "sm" && {
              fontSize: 14,
            },
            size === "lg" && {
              fontSize: 18,
            },
            outline && {
              ...FONTS.fontMedium,
            },
          ]}
        >
          {leftIcon || rightIcon ? (
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                flexDirection: "row",
                width: "100%",
                gap: 20,
              }}
            >
              {leftIcon && (
                <TouchableOpacity
                  style={{ justifyContent: "center" }}
                  onPress={onQuantityDecrease}
                >
                  <Text
                    style={{
                      fontWeight: "500",
                      fontSize: 25,
                      width: 30,
                      textAlign: "center",
                    }}
                  >
                    {leftIcon}
                  </Text>
                </TouchableOpacity>
              )}
              <Text style={{ fontWeight: "500", fontSize: 14 }}>{title}</Text>
              {rightIcon && (
                <TouchableOpacity style={{}} onPress={onQuantityIncrease}>
                  <Text
                    style={{
                      fontWeight: "500",
                      fontSize: 20,
                      width: 30,
                      justifyContent: "center",
                      textAlign: "center",
                    }}
                  >
                    {rightIcon}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <Text style={{ borderWidth: 1 }}>{title}</Text>
          )}
          {badge && (
            <View style={{ marginVertical: -4, marginLeft: 8 }}>{badge()}</View>
          )}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default Button;
