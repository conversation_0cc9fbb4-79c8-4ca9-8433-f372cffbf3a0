import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  TextInput,
} from 'react-native';
import NotificationService from '../services/NotificationService';

const SystemNotificationDemo: React.FC = () => {
  const [title, setTitle] = useState('Test Notification');
  const [body, setBody] = useState('This is a test system notification!');
  const [loading, setLoading] = useState(false);

  const sendWelcomeNotification = async () => {
    setLoading(true);
    try {
      await NotificationService.sendFirebaseNotification(
        'Welcome to Sunrise B2B! 🌅',
        'Your account is ready. Start exploring our products and services.',
        {
          type: 'welcome',
          action: 'open_app',
          timestamp: new Date().toISOString()
        }
      );
      Alert.alert('Success', 'Welcome notification sent! Check your notification tray.');
    } catch (error) {
      Alert.alert('Error', 'Failed to send notification: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const sendLoginNotification = async () => {
    setLoading(true);
    try {
      await NotificationService.sendFirebaseNotification(
        'Login Successful! 🎉',
        'Welcome back! You\'re now logged in to Sunrise B2B.',
        {
          type: 'login_success',
          action: 'open_dashboard',
          timestamp: new Date().toISOString()
        }
      );
      Alert.alert('Success', 'Login notification sent! Check your notification tray.');
    } catch (error) {
      Alert.alert('Error', 'Failed to send notification: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const sendCustomNotification = async () => {
    if (!title.trim() || !body.trim()) {
      Alert.alert('Error', 'Please enter both title and body');
      return;
    }

    setLoading(true);
    try {
      await NotificationService.sendFirebaseNotification(
        title,
        body,
        {
          type: 'custom',
          action: 'open_app',
          timestamp: new Date().toISOString()
        }
      );
      Alert.alert('Success', 'Custom notification sent! Check your notification tray.');
    } catch (error) {
      Alert.alert('Error', 'Failed to send notification: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const testLocalNotification = async () => {
    setLoading(true);
    try {
      await NotificationService.showSystemNotification(
        'Local Test Notification',
        'This is a local notification test (may not appear in system tray)',
        {
          type: 'local_test',
          timestamp: new Date().toISOString()
        }
      );
      Alert.alert('Success', 'Local notification sent!');
    } catch (error) {
      Alert.alert('Error', 'Failed to send local notification: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const showFCMToken = async () => {
    try {
      const token = await NotificationService.getFcmToken();
      if (token) {
        Alert.alert(
          'FCM Token',
          `Token: ${token.substring(0, 50)}...\n\nFull token copied to console.`,
          [{ text: 'OK' }]
        );
        console.log('📱 Full FCM Token:', token);
      } else {
        Alert.alert('Error', 'No FCM token available');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get FCM token: ' + error.message);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>System Notification Demo</Text>
      <Text style={styles.subtitle}>
        Test real system notifications that appear in the notification tray
      </Text>

      {/* Predefined Notifications */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Predefined Notifications</Text>
        
        <TouchableOpacity
          style={[styles.button, styles.welcomeButton]}
          onPress={sendWelcomeNotification}
          disabled={loading}
        >
          <Text style={styles.buttonText}>
            {loading ? 'Sending...' : 'Send Welcome Notification 🌅'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.loginButton]}
          onPress={sendLoginNotification}
          disabled={loading}
        >
          <Text style={styles.buttonText}>
            {loading ? 'Sending...' : 'Send Login Success Notification 🎉'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Custom Notification */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Custom Notification</Text>
        
        <TextInput
          style={styles.input}
          placeholder="Notification Title"
          value={title}
          onChangeText={setTitle}
          placeholderTextColor="#999"
        />
        
        <TextInput
          style={[styles.input, styles.textArea]}
          placeholder="Notification Body"
          value={body}
          onChangeText={setBody}
          multiline
          numberOfLines={3}
          placeholderTextColor="#999"
        />
        
        <TouchableOpacity
          style={[styles.button, styles.customButton]}
          onPress={sendCustomNotification}
          disabled={loading}
        >
          <Text style={styles.buttonText}>
            {loading ? 'Sending...' : 'Send Custom Notification 📱'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Test Options */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test Options</Text>
        
        <TouchableOpacity
          style={[styles.button, styles.localButton]}
          onPress={testLocalNotification}
          disabled={loading}
        >
          <Text style={styles.buttonText}>
            {loading ? 'Sending...' : 'Test Local Notification 🔔'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.tokenButton]}
          onPress={showFCMToken}
        >
          <Text style={styles.buttonText}>Show FCM Token 🔑</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.info}>
        <Text style={styles.infoTitle}>ℹ️ How to Test System Notifications:</Text>
        <Text style={styles.infoText}>
          1. Set up your backend server (see backend-example folder)
        </Text>
        <Text style={styles.infoText}>
          2. Update BACKEND_URL in NotificationService.ts
        </Text>
        <Text style={styles.infoText}>
          3. Tap notification buttons above
        </Text>
        <Text style={styles.infoText}>
          4. Check your device's notification tray
        </Text>
        <Text style={styles.infoText}>
          5. Notifications should appear even when app is closed
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  welcomeButton: {
    backgroundColor: '#FF6B35',
  },
  loginButton: {
    backgroundColor: '#4CAF50',
  },
  customButton: {
    backgroundColor: '#2196F3',
  },
  localButton: {
    backgroundColor: '#FF9800',
  },
  tokenButton: {
    backgroundColor: '#9C27B0',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  info: {
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#1976d2',
    marginBottom: 5,
  },
});

export default SystemNotificationDemo;
