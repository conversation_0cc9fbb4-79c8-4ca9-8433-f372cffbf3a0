import React, { useCallback, useContext, useState, useEffect } from "react";
import { View, Text, Image, TouchableOpacity, ScrollView } from "react-native";

import { COLORS, FONTS, FONTWEIGHT, SIZES } from "../../constants/theme";
import { GlobalStyleSheet } from "../../constants/StyleSheet";
import Badge from "../Badge/Badge";
import BottomNavModal from "../BottomSheetNew/BottomSheetNew";
import { GET_VARIANT_BY_PRODUCT_HANDLE } from "../../api/homepageQuery";
import client from "../../api/appoloClient";
import { gql, useQuery, useLazyQuery } from "@apollo/client";
import CartIcon from "../../assets/icons/addtocart.png";
import { useNavigation } from "@react-navigation/native";

export default function AddToCartCard({ products, children }: any) {
  const navigation = useNavigation();
  const varients = ["black", "gray", "brown", "black", "gray", "brown"];
  const [modalVisible, setModalVisible] = useState(false);
  const [currentProductDetails, setCurrentProductDetails] = useState(null);
  const [productVariants, setProductVariants] = useState(products);
  // Apollo Lazy Query to fetch variants on demand
  const [fetchVariants, { loading, data }] = useLazyQuery(
    GET_VARIANT_BY_PRODUCT_HANDLE,
    {
      fetchPolicy: "network-only",
    }
  );

  const openBottomSheet = useCallback(
    (product: any) => {
      setModalVisible(true);
      setCurrentProductDetails(product);
      fetchVariants({ variables: { handle: product.handle } });
    },
    [modalVisible]
  );

  // Update variants when data is received
  useEffect(() => {
    if (data?.productByHandle?.variants?.edges) {
      setProductVariants(
        data?.productByHandle.variants.edges?.map((edge: any) => edge.node)
      );
    }
  }, [data]);

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
      scrollEventThrottle={16}
      style={{ backgroundColor: COLORS.background }}
    >
      <View
        style={[
          GlobalStyleSheet.row,
          {
            flexWrap: "nowrap",
            backgroundColor: COLORS.background,
            marginRight: 16,
          },
        ]}
      >
        {children}
        {products?.map((data: any, index: any) => {
          const varientsColor = data?.variants?.map((item: any) => {
            return item.title.toLowerCase();
          });

          return (
            <View style={[{ marginHorizontal: 5 }]} key={index}>
              <Badge
                title="Pre order"
                color={COLORS.badgeBackgroundColor}
                size="md"
                style={{
                  position: "absolute",
                  zIndex: 10000,
                  borderRadius: 8,
                  marginTop: 5,
                  marginHorizontal: 5,
                }}
              />
              <TouchableOpacity
                activeOpacity={0.7}
                style={{
                  borderWidth: 1,
                  borderColor: "white",
                  borderRadius: 15,
                  backgroundColor: COLORS.card,
                }}
                onPress={() => {
                  navigation.navigate("ProductDetailsPage", {
                    handle: data?.handle,
                  });
                }}
              >
                <TouchableOpacity
                  onPress={() => openBottomSheet(data)}
                  style={{
                    position: "absolute",
                    zIndex: 1000,
                    backgroundColor: COLORS.addToCartBg,
                    width: 40,
                    height: 40,
                    top: 120,
                    right: 0,
                    borderTopLeftRadius: 8,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Image
                    source={CartIcon}
                    style={{
                      width: 40,
                      height: 40,
                    }}
                  />
                </TouchableOpacity>

                <Image
                  style={{
                    width: "100%",
                    height: 160,
                    aspectRatio: 2 / 2,
                    borderTopLeftRadius: 15,
                    borderTopRightRadius: 15,
                  }}
                  source={{
                    uri: data?.image
                      ? data?.image
                      : data.featuredImage?.url
                      ? data.featuredImage?.url
                      : "https://img.freepik.com/premium-vector/colorful-silhouette-badminton-player-action-vector-illustration_968957-7304.jpg?w=360",
                  }}
                />

                <View
                  style={{
                    flexDirection: "column",
                    justifyContent: "space-between",
                    marginVertical: 5,
                    marginHorizontal: 8,
                    marginTop: 3,
                    width: 130,
                    height: 100,
                  }}
                >
                  <Text
                    style={{
                      ...FONTS.fontRegular,
                      fontSize: SIZES.fontXs,
                      color: COLORS.textBrandName,
                      ...FONTS.fontRegular,
                    }}
                  >
                    {data?.vendor}
                  </Text>

                  <Text
                    style={{
                      width: "auto",
                      ...FONTWEIGHT.SemiBold,
                      ...FONTS.fontRegular,
                      fontSize: SIZES.font,
                      fontFamily: "DMSansSemiBold",
                    }}
                    numberOfLines={2}
                    ellipsizeMode="tail"
                  >
                    {data?.title}
                  </Text>

                  <View style={{ flexDirection: "row", gap: 5 }}>
                    <Text
                      style={{
                        marginVertical: 5,
                        fontSize: SIZES.font,
                        ...FONTWEIGHT.SemiBold,
                        fontFamily: "DMSansSemiBold",
                      }}
                    >
                      $
                      {data?.priceRange?.maxVariantPrice?.amount
                        ? data?.priceRange?.maxVariantPrice?.amount
                        : data?.priceRange?.maxVariantPrice?.amount
                        ? data?.priceRange?.minVariantPrice?.amount
                        : data?.priceRange?.minVariantPrice?.amount ?? 15}
                    </Text>
                    {/* <Text
                      style={{
                        marginVertical: 5,
                        color: COLORS.darkgray,
                        fontSize: SIZES.font,
                        ...FONTWEIGHT.Light,
                        textDecorationLine: "line-through",
                        marginHorizontal: 2,
                        fontFamily: "DMSansMedium",
                      }}
                    >
                      $126.00
                    </Text> */}
                  </View>
                  <View
                    style={{
                      flexDirection: "row",
                      gap: 10,
                    }}
                  >
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        gap: 4,
                        alignItems: "center",
                      }}
                    >
                      {varientsColor?.slice(0, 3)?.map((varient: any) => {
                        return (
                          <>
                            <View
                              key={varient}
                              style={{
                                width: 15,
                                height: 15,
                                backgroundColor: `${varient}`,
                                borderRadius: 25,
                              }}
                            ></View>
                          </>
                        );
                      })}
                    </View>
                    {varientsColor?.length > 3 && (
                      <Text style={{ fontFamily: "DMSansLight" }}>
                        +{varientsColor?.slice(3)?.length}
                      </Text>
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          );
        })}
      </View>
      <BottomNavModal
        modalVisible={modalVisible}
        setModalVisible={setModalVisible}
        currentProductDetails={productVariants}
        isBackBtnRequired={false}
        isCloseButtonRequired={false}
        modelTitle={currentProductDetails?.title}
      />
    </ScrollView>
  );
}
