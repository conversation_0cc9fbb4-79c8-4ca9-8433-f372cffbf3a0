import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import { COLORS, FONTS, FONTWEIGHT } from "../../constants/theme";
import { useNavigation } from "@react-navigation/native";

export default function CardHeader({ cardHeaderTitle, viewAllProducts }: any) {
  const navigation = useNavigation();
  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <Text
        style={{
          ...FONTS.fontMedium,
          color: COLORS.black,
          ...FONTWEIGHT.SemiBold,
          ...FONTS.h4,
        }}
      >
        {cardHeaderTitle}
      </Text>
      <TouchableOpacity
        onPress={() => {
          navigation.navigate("Category");
        }}
      >
        <Text
          style={{
            ...FONTS.fontMedium,
            fontSize: 14,
            color: COLORS.redirectionUrl,
            textDecorationLine: "underline",
            ...FONTWEIGHT.SemiBold,
          }}
        >
          {viewAllProducts?.title}
        </Text>
      </TouchableOpacity>
    </View>
  );
}
