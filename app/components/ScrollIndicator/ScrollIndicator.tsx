import { View, Text } from "react-native";
import React from "react";
import { COLORS } from "../../constants/theme";

type Props = {
  images: any;
  activeIndex?: any;
  styles?:any
};
export default function ScrollIndicator({ images, activeIndex,styles }: Props) {
  return (
    <View
      style={{
        flexDirection: "row",
        marginTop: 10,
        position: "absolute",
        ...styles,
      }}
    >
      {images.map((_: any, index: any) => {
        return (
          <View
            key={index}
            style={{
              width: activeIndex === index ? 20 : 5,
              height: 6,
              borderRadius: 5,
              marginHorizontal: 5,
              backgroundColor:
                activeIndex === index ? COLORS.black : COLORS.white,
            }}
          />
        );
      })}
    </View>
  );
}
