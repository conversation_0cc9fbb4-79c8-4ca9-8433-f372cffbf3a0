import React, { memo, useCallback, useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableWithoutFeedback,
  Image,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  Dimensions,
} from "react-native";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import {
  ButtonLabel,
  COLORS,
  FONTS,
  FONTWEIGHT,
  SIZES,
} from "../../constants/theme";
import Button from "../Button/Button";
import Header from "../../layout/Header";
import { useMutation } from "@apollo/client";
import { ADD_LINES_TO_CART, CREATE_CART } from "../../api/cartQuery";
import { useNavigation } from "@react-navigation/native";
import { ActivityIndicator } from "react-native-paper";
import AddIcon from "../../assets/icons/addicon.png";
import RemoveIcon from "../../assets/icons/removeIcon.png";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  runOnJS,
} from "react-native-reanimated";
import { useDispatch, useSelector } from "react-redux";
import { setCartId } from "../../redux/reducer/cartReducer";
interface ButtonProps {
  modalVisible?: any;
  setModalVisible?: any;
  currentProductDetails?: any;
  buttonWidth?: any;
  children?: any;
  height?: any;
  buttonTitle?: any;
  leftbuttonTitle?: any;
  rightButtonTitle?: any;
  leftIcon?: any;
  rightIcon?: any;
  clearAllBtn?: any;
  headerEnabled?: any;
  noButtton?: any;
  planBottomSheet?: any;
  navbarTitle?: any;
  isCloseButtonRequired?: any;
  isBackBtnRequired?: any;
  modelTitle?: any;
  onPressRightBtn?: any;
  onPressLeftBtn?: any;
  leftIconWidth?: any;
  modelTitleTextComponent?: any;
  onQuantityDecrease?: () => void;
  onQuantityIncrease?: () => void;
}
const BottomNavModal: React.FC<ButtonProps> = ({
  modalVisible,
  setModalVisible,
  currentProductDetails,
  buttonWidth,
  children,
  height,
  leftbuttonTitle = false,
  rightButtonTitle = false,
  leftIcon = false,
  rightIcon = false,
  clearAllBtn = false,
  headerEnabled = false,
  noButtton = false,
  buttonTitle,
  planBottomSheet = false,
  navbarTitle,
  isCloseButtonRequired = true,
  isBackBtnRequired = true,
  modelTitle = "",
  onPressRightBtn = () => {},
  onPressLeftBtn = () => {},
  leftIconWidth = false,
  modelTitleTextComponent = null,
  onQuantityDecrease = () => {},
  onQuantityIncrease = () => {},
}: any) => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { cartId } = useSelector((state: any) => state.cart);

  const closeBottomSheet = useCallback(() => {
    setModalVisible(false);
  }, [modalVisible]);
  const [quantities, setQuantities] = useState<{ [variantId: string]: number }>(
    {}
  );
  const [createCart, { loading, error }] = useMutation(CREATE_CART);
  const [addCart, { loading: addLoading }] = useMutation(ADD_LINES_TO_CART);
  const modalAnimY = useSharedValue(1000); // Initialize the shared value for Y translation
  const [isRendered, setIsRendered] = useState(modalVisible);
  const screenHeight = Dimensions.get("screen").height;
  useEffect(() => {
    if (modalVisible) {
      setIsRendered(true); // Ensure the modal is rendered before animating
      modalAnimY.value = withTiming(0, {
        duration: 500, // Slow down the animation by increasing the duration
        easing: Easing.out(Easing.quad), // Use a more gentle easing for a smoother effect
      });
    } else {
      modalAnimY.value = withTiming(
        1000,
        {
          duration: 300,
          easing: Easing.in(Easing.ease), // Smooth easing for closing animation
        },
        (finished) => {
          if (finished) {
            runOnJS(setIsRendered)(false); // Hide the modal after animation
          }
        }
      );
    }
  }, [modalVisible]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: modalAnimY.value }],
    };
  });

  useEffect(() => {
    if (modalVisible) {
      setQuantities({});
    }
  }, [modalVisible]);

  const handleIncrease = (variantId: string) => {
    setQuantities((prev) => ({
      ...prev,
      [variantId]: (prev[variantId] || 1) + 1,
    }));
  };

  const handleDecrease = (variantId: string) => {
    setQuantities((prev) => ({
      ...prev,
      [variantId]: Math.max(1, (prev[variantId] || 1) - 1),
    }));
  };

  const handleAddToBag = () => {
    if (loading || addLoading) return;
    if (!currentProductDetails || currentProductDetails.length === 0) {
      alert("No product selected.");
      return;
    }

    const cartLines = currentProductDetails
      .filter(
        (variant: any) => quantities[variant.id] && quantities[variant.id] > 0
      )
      .map((variant: any) => ({
        merchandiseId: variant.id,
        quantity: quantities[variant.id],
      }));

    if (cartLines.length === 0) {
      alert("Please select at least one product");
      return;
    }
    // console.log("CartLines: ", cartLines);

    if (cartId) {
      addCart({
        variables: {
          cartId,
          lines: cartLines,
        },
      })
        .then((response: any) => {
          if (response.data.cartLinesAdd.userErrors.length > 0) {
            alert("Error: " + response.data.cartLinesAdd.userErrors[0].message);
          } else {
            Alert.alert("Success", "Added to cart successfully!", [
              {
                text: "OK",
                onPress: () => {
                  const cartId = response.data.cartLinesAdd.cart.id;
                  // console.log("Cart Id: ", cartId);
                  dispatch(setCartId(cartId));
                  navigation.push("MyCart", {
                    cartId,
                    cartLines,
                  });
                  closeBottomSheet();
                },
              },
            ]);
            // closeBottomSheet()
          }
        })
        .catch((err) => {
          alert("Something went wrong. Please try again.");
          console.error("Cart addition error:", err);
        });
    } else {
      createCart({
        variables: {
          lines: cartLines,
        },
      })
        .then((response: any) => {
          if (response.data.cartCreate.userErrors.length > 0) {
            alert("Error: " + response.data.cartCreate.userErrors[0].message);
          } else {
            Alert.alert("Success", "Added to cart successfully!", [
              {
                text: "OK",
                onPress: () => {
                  const cartId = response.data.cartCreate.cart.id;
                  // console.log("Cart Id: ", cartId);
                  dispatch(setCartId(cartId));
                  navigation.push("MyCart", {
                    cartId,
                    cartLines,
                  });
                  closeBottomSheet();
                },
              },
            ]);
            // closeBottomSheet()
          }
        })
        .catch((err) => {
          alert("Something went wrong. Please try again.");
          console.error("Cart creation error:", err);
        });
    }
  };

  return (
    <SafeAreaProvider>
      {isRendered && (
        <Modal
          animationType="none"
          transparent={true}
          visible={true}
          onRequestClose={() => setModalVisible(false)}
        >
          <TouchableWithoutFeedback onPress={() => closeBottomSheet()}>
            <View style={styles.modalOverlay}>
              <TouchableWithoutFeedback>
                <Animated.View
                  style={[
                    animatedStyle,
                    styles.bottomNavContainer,
                    {
                      maxHeight: height ? height : "80%",
                      height: height,
                      borderTopLeftRadius: noButtton ? 20 : height ? 0 : 20,
                      borderTopRightRadius: noButtton ? 20 : height ? 0 : 20,
                      backgroundColor: COLORS.background,
                      // paddingRight: 5,
                      padding: planBottomSheet ? 0 : 20,
                      // gap: planBottomSheet?10:0,
                    },
                  ]}
                >
                  {headerEnabled === false
                    ? planBottomSheet && (
                        <View
                          style={{
                            marginTop: screenHeight / 15,
                            marginHorizontal: 12,
                          }}
                        >
                          <Header
                            title={navbarTitle}
                            leftIcon={isBackBtnRequired ? "back" : null}
                            rightIcon={isCloseButtonRequired && "close"}
                            rightIconhandler={closeBottomSheet}
                            backAction={closeBottomSheet}
                          />
                        </View>
                      )
                    : null}
                  {headerEnabled && !planBottomSheet && (
                    <Header
                      title={navbarTitle || ""}
                      leftIcon="back"
                      rightIcon={isCloseButtonRequired && "close"}
                      rightIconhandler={closeBottomSheet}
                    />
                  )}
                  {modelTitleTextComponent
                    ? modelTitleTextComponent
                    : modelTitle && (
                        <Text
                          style={{
                            marginTop: noButtton ? 0 : 5,
                            marginBottom: noButtton || navbarTitle ? 0 : 20,
                            marginHorizontal: 10,
                            fontSize: SIZES.fontLg,
                            color: COLORS.title,
                            fontFamily: "DMSansSemiBold",
                          }}
                        >
                          {modelTitle}
                        </Text>
                      )}

                  <ScrollView
                    style={styles.scrollContainer}
                    showsVerticalScrollIndicator={false}
                  >
                    {children ? (
                      children
                    ) : (
                      <>
                        {currentProductDetails?.map((variant: any) => (
                          <View key={variant.id} style={styles.navItems}>
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                              }}
                            >
                              <View style={{ flexDirection: "row", gap: 10 }}>
                                <Image
                                  style={styles.productImage}
                                  source={{ uri: variant.image?.url }}
                                />
                                <View
                                  style={{ gap: 8, justifyContent: "center" }}
                                >
                                  <Text
                                    style={{
                                      // ...FONTWEIGHT.Bold,
                                      fontSize: SIZES.font,
                                      ...FONTS.fontSemiBold,
                                    }}
                                  >
                                    {variant?.title}
                                  </Text>
                                  <Text
                                    style={{
                                      color: COLORS.textBrandName,
                                      fontSize: SIZES.fontXs,
                                      ...FONTS.fontRegular,
                                    }}
                                  >
                                    SKU: {variant.sku || "N/A"}
                                  </Text>
                                  <Text
                                    style={{
                                      ...FONTWEIGHT.Normal,
                                      fontSize: SIZES.fontXs,
                                      ...FONTS.fontRegular,
                                    }}
                                  >
                                    ${variant.price?.amount}
                                  </Text>
                                </View>
                              </View>
                              {/* <View
                                style={{
                                  flexDirection: "row",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  columnGap: 10, // or use marginHorizontal on buttons if not supported
                                }}
                              >
                                <TouchableOpacity
                                  onPress={() => handleDecrease(variant.id)}
                                  style={styles.quantityButton}
                                >
                                  <Image
                                    style={{ width: 15, height: 15 }}
                                    source={RemoveIcon}
                                  />
                                </TouchableOpacity>

                                <TextInput
                                  style={{
                                    fontSize: SIZES.font,
                                    width: 60, // enough to fit 4-digit number
                                    // height: 36,
                                    textAlign: "center",
                                    borderWidth: 1,
                                    borderColor: "#ccc",
                                    borderRadius: 5,
                                    // paddingVertical: 2,
                                    // paddingHorizontal: 6,
                                  }}
                                  value={String(quantities[variant.id] || 1)}
                                  keyboardType="numeric"
                                  onChangeText={(text) =>
                                    handleManualInput(variant.id, text)
                                  }
                                  maxLength={4}
                                />

                                <TouchableOpacity
                                  onPress={() => handleIncrease(variant.id)}
                                  style={styles.quantityButton}
                                >
                                  <Image
                                    style={{ width: 15, height: 15 }}
                                    source={AddIcon}
                                  />
                                </TouchableOpacity>
                              </View> */}
                              <QuantityInput
                                variant={variant}
                                quantities={quantities}
                                setQuantities={setQuantities}
                                RemoveIcon={RemoveIcon}
                                AddIcon={AddIcon}
                              />
                            </View>
                          </View>
                        ))}
                      </>
                    )}
                  </ScrollView>
                  {children
                    ? isCloseButtonRequired && (
                        <View
                          style={{
                            // marginTop: 10,
                            flexDirection: "row",
                            justifyContent: "space-between",
                            alignItems: "center",
                            gap: 10,
                            paddingVertical: 20,
                            paddingHorizontal: clearAllBtn ? 0 : 5,
                            backgroundColor: COLORS.background,
                          }}
                        >
                          {clearAllBtn && (
                            <View
                              style={{
                                gap: 12,
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                width: "100%",
                                // borderWidth:1
                              }}
                            >
                              <Button
                                title={buttonTitle}
                                onPress={onPressLeftBtn}
                                btnRounded={true}
                                color={COLORS.white}
                                onQuantityDecrease={onQuantityDecrease}
                                onQuantityIncrease={onQuantityIncrease}
                                outline={true}
                                style={{
                                  width: clearAllBtn
                                    ? leftIconWidth
                                      ? leftIconWidth
                                      : 160
                                    : 150,
                                  height: 60,
                                }}
                                leftIcon={leftIcon}
                                rightIcon={rightIcon}
                              />
                              <Button
                                title={
                                  rightButtonTitle
                                    ? rightButtonTitle
                                    : ButtonLabel.addToBag
                                }
                                onPress={onPressRightBtn}
                                btnRounded={true}
                                style={{ width: buttonWidth }}
                              />
                            </View>
                          )}
                        </View>
                      )
                    : !planBottomSheet && (
                        <View style={{ marginTop: 10, marginHorizontal: 10 }}>
                          <Button
                            title={ButtonLabel.addToBag}
                            onPress={async () => {
                              await handleAddToBag();
                            }}
                            btnRounded={true}
                            style={{ width: buttonWidth }}
                            loading={loading || addLoading}
                          />
                        </View>
                      )}
                </Animated.View>
              </TouchableWithoutFeedback>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      )}
    </SafeAreaProvider>
  );
};

const QuantityInput = ({
  variant,
  quantities,
  setQuantities,
  RemoveIcon,
  AddIcon,
}: any) => {
  const value = String(quantities[variant.id] || 0);

  const [inputWidth, setInputWidth] = useState(80 + value.length * 10); // initial width

  const handleManualInput = (variantId: any, text: any) => {
    const numeric = text.replace(/[^0-9]/g, "");

    setQuantities((prev: any) => ({
      ...prev,
      [variantId]: numeric === "" ? "" : parseInt(numeric, 10),
    }));
    // Estimate width: base + 10px per digit
    setInputWidth(30 + Math.max(numeric.length, 1) * 10);
  };

  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        // gap: 10,
      }}
    >
      <TouchableOpacity
        onPress={() => {
          const newVal = Math.max(0, (quantities[variant.id] || 1) - 1);
          setQuantities((prev: any) => ({ ...prev, [variant.id]: newVal }));
        }}
        style={styles.quantityButton}
      >
        <Image style={{ width: 15, height: 15 }} source={RemoveIcon} />
      </TouchableOpacity>

      <TextInput
        style={{
          fontSize: 16,
          width: 60,
          height: 31,
          textAlign: "center",
          borderWidth: 1,
          borderColor: "#ccc",
          // borderRadius: 6,
          paddingVertical: 2,
          paddingHorizontal: 4,
        }}
        keyboardType="numeric"
        value={value}
        onChangeText={(text) => handleManualInput(variant.id, text)}
        maxLength={7}
      />

      <TouchableOpacity
        onPress={() => {
          const newVal = (quantities[variant.id] || 0) + 1;
          setQuantities((prev: any) => ({ ...prev, [variant.id]: newVal }));
        }}
        style={styles.quantityButton1}
      >
        <Image style={{ width: 15, height: 15 }} source={AddIcon} />
      </TouchableOpacity>
    </View>
  );
};
const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  bottomNavContainer: {
    backgroundColor: "white",
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "80%",
  },
  scrollContainer: {
    width: "100%",
  },
  modalText: {
    fontSize: SIZES.fontLg,
    ...FONTWEIGHT.Medium,
    marginBottom: 10,
  },
  navItems: {
    gap: 10,
    marginBottom: 15,
  },
  productImage: {
    width: 70,
    height: 70,
    borderRadius: 10,
  },
  quantityContainer: {
    width: 120,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-evenly",
    alignSelf: "center",
    borderWidth: 0.8,
    borderColor: COLORS.darkgray,
    height: 30,
    borderRadius: 8,
    gap: 10,
    marginHorizontal: 20,
  },
  quantityButton: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 6,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    paddingTop: 10,
    alignItems: "center",
    // borderWidth:1
  },
  quantityButton1: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 6,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    paddingTop: 10,
    alignItems: "center",
    // borderWidth:1
  },
});

export default memo(BottomNavModal);
