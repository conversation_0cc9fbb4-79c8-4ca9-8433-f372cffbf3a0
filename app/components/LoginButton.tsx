import React from 'react';
import { TouchableOpacity, Text, StyleSheet, Linking } from 'react-native';

interface LoginButtonProps {
  onLoginSuccess: (userData: any) => void;
}

const LoginButton: React.FC<LoginButtonProps> = ({ onLoginSuccess }) => {
  const handleLogin = () => {
    // Simply redirect to Shopify login page
    const loginUrl = 'https://sunrise-trade.myshopify.com/account/login';
    Linking.openURL(loginUrl);
  };

  return (
    <TouchableOpacity style={styles.button} onPress={handleLogin}>
      <Text style={styles.text}>Login with Shopify</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#5C6AC4',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    margin: 10,
  },
  text: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default LoginButton; 