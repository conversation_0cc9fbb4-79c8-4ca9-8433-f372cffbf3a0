import React from "react";
import { View, Text } from "react-native";
import { COLORS, FONTS, SIZES } from "../../constants/theme";

const Badge = ({ title, color, style, rounded, size, text }: any) => {
  return (
    <View
      style={[
        {
          paddingHorizontal: 5,
          paddingVertical: 5,
          backgroundColor: color ? color : COLORS.primary,
          height: 20,
          alignItems: "center",
          borderRadius: SIZES.radius,
        },
        style && {
          ...style,
        },
        rounded && {
          borderRadius: 30,
        },
        size === "md" && {
          paddingHorizontal: 10,
          paddingVertical: 6,
          height: 26,
        },
        size === "lg" && {
          paddingHorizontal: 10,
          paddingVertical: 5,
          height: 32,
        },
      ]}
    >
      <Text
        style={[
          {
            ...FONTS.fontMedium,
            ...FONTS.fontXs,
            color: COLORS.white,
          },
          (color === COLORS.light ||
            color === COLORS.white ||
            text === COLORS.title) && {
            color: COLORS.title,
            ...FONTS.fontMedium,
          },
          size === "md" && {
            fontSize: 13,
          },
          size === "lg" && {
            fontSize: 15,
            lineHeight: 20,
          },
          {
            paddingTop: 2,
          },
        ]}
      >
        {title}
      </Text>
    </View>
  );
};

export default Badge;
