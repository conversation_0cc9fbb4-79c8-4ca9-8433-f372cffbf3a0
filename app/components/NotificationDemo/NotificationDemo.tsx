import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { COLORS, FONTS } from '../../constants/theme';
import NotificationService from '../../services/NotificationService';
import AsyncStorage from '@react-native-async-storage/async-storage';

const NotificationDemo = () => {
  const [fcmToken, setFcmToken] = useState<string | null>(null);
  const [notificationStats, setNotificationStats] = useState({ total: 0, unread: 0, read: 0 });
  const [isFirstLaunch, setIsFirstLaunch] = useState<boolean | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      // Get FCM token
      const token = await AsyncStorage.getItem('fcmToken');
      setFcmToken(token);

      // Get notification stats
      const stats = await NotificationService.getNotificationStats();
      setNotificationStats(stats);

      // Check if first launch
      const firstLaunch = await AsyncStorage.getItem('isFirstLaunch');
      setIsFirstLaunch(firstLaunch === null);
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  const handleTestWelcomeNotification = async () => {
    try {
      await NotificationService.sendWelcomeNotification();
      Alert.alert('Success', 'Welcome notification sent!');
      loadData();
    } catch (error) {
      Alert.alert('Error', 'Failed to send welcome notification');
    }
  };

  const handleTestLoginNotification = async () => {
    try {
      await NotificationService.onLoginSuccess({
        accessToken: 'test-token',
        customerData: { id: 'test-user', firstName: 'Test User' }
      });
      Alert.alert('Success', 'Login notification sent!');
      loadData();
    } catch (error) {
      Alert.alert('Error', 'Failed to send login notification');
    }
  };

  const handleTestCompanyVerification = async () => {
    try {
      await NotificationService.onCompanyVerified();
      Alert.alert('Success', 'Company verification notification sent!');
      loadData();
    } catch (error) {
      Alert.alert('Error', 'Failed to send company verification notification');
    }
  };

  const handleTestCustomNotification = async () => {
    try {
      await NotificationService.showSystemNotification(
        'Custom Test Notification 🚀',
        'This is a custom test notification to demonstrate the notification system.',
        {
          type: 'test',
          action: 'open_app',
          timestamp: new Date().toISOString()
        }
      );
      Alert.alert('Success', 'Custom system notification sent!');
      loadData();
    } catch (error) {
      Alert.alert('Error', 'Failed to send custom notification');
    }
  };

  const handleTestSystemNotification = async () => {
    try {
      await NotificationService.showSystemNotification(
        'System Notification Test 📱',
        'This notification should appear in your device notification tray!',
        {
          type: 'system_test',
          action: 'open_app',
          timestamp: new Date().toISOString()
        }
      );
      Alert.alert('Success', 'System notification sent! Check your notification tray.');
      loadData();
    } catch (error) {
      Alert.alert('Error', 'Failed to send system notification');
    }
  };

  const handleResetFirstLaunch = async () => {
    try {
      await AsyncStorage.removeItem('isFirstLaunch');
      await AsyncStorage.removeItem('isFirstInstall');
      setIsFirstLaunch(true);
      Alert.alert('Success', 'First launch status reset! Restart the app to see welcome notification.');
    } catch (error) {
      Alert.alert('Error', 'Failed to reset first launch status');
    }
  };

  const handleRefreshToken = async () => {
    try {
      const token = await NotificationService.getFcmToken();
      setFcmToken(token);
      Alert.alert('Success', 'FCM token refreshed!');
    } catch (error) {
      Alert.alert('Error', 'Failed to refresh FCM token');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🔔 Notification Demo</Text>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📊 Notification Stats</Text>
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{notificationStats.total}</Text>
            <Text style={styles.statLabel}>Total</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{notificationStats.unread}</Text>
            <Text style={styles.statLabel}>Unread</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{notificationStats.read}</Text>
            <Text style={styles.statLabel}>Read</Text>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🔑 FCM Token</Text>
        <Text style={styles.tokenText} numberOfLines={3}>
          {fcmToken ? fcmToken : 'No token available'}
        </Text>
        <TouchableOpacity style={styles.button} onPress={handleRefreshToken}>
          <Text style={styles.buttonText}>Refresh Token</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🚀 App Status</Text>
        <Text style={styles.infoText}>
          First Launch: {isFirstLaunch === null ? 'Unknown' : isFirstLaunch ? 'Yes' : 'No'}
        </Text>
        <TouchableOpacity style={styles.button} onPress={handleResetFirstLaunch}>
          <Text style={styles.buttonText}>Reset First Launch</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🧪 Test Notifications</Text>

        <TouchableOpacity style={styles.testButton} onPress={handleTestWelcomeNotification}>
          <Text style={styles.buttonText}>🌅 Test Welcome Notification</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.testButton} onPress={handleTestLoginNotification}>
          <Text style={styles.buttonText}>🔐 Test Login Notification</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.testButton} onPress={handleTestCompanyVerification}>
          <Text style={styles.buttonText}>✅ Test Company Verification</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.testButton} onPress={handleTestCustomNotification}>
          <Text style={styles.buttonText}>🚀 Test Custom Notification</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.testButton} onPress={handleTestSystemNotification}>
          <Text style={styles.buttonText}>📱 Test System Notification</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.infoText}>
          💡 Tip: Check the Notifications screen to see all received notifications.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.primary,
    textAlign: 'center',
    marginBottom: 20,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: 10,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.textLight,
    marginTop: 5,
  },
  tokenText: {
    fontSize: 12,
    color: COLORS.textLight,
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: COLORS.textLight,
    marginBottom: 10,
  },
  button: {
    backgroundColor: COLORS.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 5,
  },
  testButton: {
    backgroundColor: COLORS.secondary,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default NotificationDemo;
