import React from 'react';
import { Modal, Text, TouchableOpacity, View, StyleSheet } from 'react-native';
import { useTheme } from '@react-navigation/native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import CustomButton from '../CustomButton';

interface TaskModalProps {
  visible: boolean;
  onClose: () => void;
  onOkay: () => void;
  taskText: string;
  title?: string;
}

const TaskModal: React.FC<TaskModalProps> = ({
  visible,
  onClose,
  onOkay,
  taskText,
  title = 'Confirmation',
}) => {
  const { colors } = useTheme();

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <TouchableOpacity
        activeOpacity={1}
        style={styles.modalOverlay}
        onPress={onClose}
      >
        <View 
          style={[
            styles.modalContainer, 
            { backgroundColor: colors.card }
          ]}
          // This prevents the modal from closing when clicking inside it
          onStartShouldSetResponder={() => true}
          onTouchEnd={(e) => e.stopPropagation()}
        >
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.title }]}>
              {title}
            </Text>
          </View>
          
          {/* Modal Content */}
          <View style={styles.modalContent}>
            <Text style={[styles.taskText, { color: colors.text }]}>
              {taskText}
            </Text>
          </View>
          
          {/* Modal Footer with Buttons */}
          <View style={styles.buttonContainer}>
            <CustomButton
              title="Cancel"
              color={COLORS.danger}
              btnSm
              style={styles.cancelButton}
              onPress={onClose}
            />
            <CustomButton
              title="Okay"
              btnSm
              style={styles.okayButton}
              onPress={onOkay}
            />
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '85%',
    maxWidth: 400,
    borderRadius: SIZES.radius_lg,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalHeader: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    alignItems: 'center',
  },
  modalTitle: {
    ...FONTS.h5,
    fontFamily: 'JostSemiBold',
  },
  modalContent: {
    padding: 20,
  },
  taskText: {
    ...FONTS.font,
    textAlign: 'center',
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 20,
    paddingTop: 0,
  },
  cancelButton: {
    marginRight: 10,
    minWidth: 100,
  },
  okayButton: {
    minWidth: 100,
  },
});

export default TaskModal;
