import React from 'react';
import { 
  View, 
  Text, 
  Modal, 
  StyleSheet, 
  TouchableOpacity, 
  TouchableWithoutFeedback 
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { Feather, AntDesign } from '@expo/vector-icons';

interface StatusModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  message: string;
  status: 'success' | 'error' | 'warning' | 'info';
}

const StatusModal: React.FC<StatusModalProps> = ({
  visible,
  onClose,
  title,
  message,
  status = 'success',
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return COLORS.success || '#4CAF50';
      case 'error':
        return COLORS.danger || '#F44336';
      case 'warning':
        return COLORS.warning || '#FF9800';
      case 'info':
        return COLORS.info || '#2196F3';
      default:
        return COLORS.success || '#4CAF50';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <AntDesign name="checkcircle" size={50} color={getStatusColor()} />;
      case 'error':
        return <AntDesign name="closecircle" size={50} color={getStatusColor()} />;
      case 'warning':
        return <AntDesign name="exclamationcircle" size={50} color={getStatusColor()} />;
      case 'info':
        return <AntDesign name="infocirlce" size={50} color={getStatusColor()} />;
      default:
        return <AntDesign name="checkcircle" size={50} color={getStatusColor()} />;
    }
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContainer}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={onClose}
              >
                <Feather name="x" size={24} color={COLORS.text} />
              </TouchableOpacity>
              
              <View style={styles.iconContainer}>
                {getStatusIcon()}
              </View>
              
              <Text style={styles.title}>{title}</Text>
              <Text style={styles.message}>{message}</Text>
              
              <TouchableOpacity
                style={[styles.button, { backgroundColor: getStatusColor() }]}
                onPress={onClose}
              >
                <Text style={styles.buttonText}>OK</Text>
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '85%',
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: 20,
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 1,
  },
  iconContainer: {
    marginVertical: 20,
  },
  title: {
    ...FONTS.h5,
    color: COLORS.title,
    marginBottom: 10,
    textAlign: 'center',
  },
  message: {
    ...FONTS.fontRegular,
    fontSize: 16,
    color: COLORS.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: SIZES.radius_sm,
    marginTop: 10,
  },
  buttonText: {
    ...FONTS.fontMedium,
    fontSize: 16,
    color: COLORS.white,
  },
});

export default StatusModal;
