import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Image, Platform } from 'react-native';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import { COLORS, FONTS, } from '../../constants/theme';
import { useTheme } from '@react-navigation/native';
import { IMAGES } from '../../constants/Images';

type Props = {
    shortRef :any
}

const ShortSheet2 = ({shortRef} : Props) => {

    const theme = useTheme();
    const { colors } : {colors : any} = theme;

    const GenderData = ["Men", "Women",];

    const [activeSize, setActiveSize] = useState(GenderData[0]);

    return (
        <View style={[GlobalStyleSheet.container, { paddingTop:Platform.OS === 'web' ?  15 : 0 }]}>
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    borderBottomWidth: 1,
                    borderBottomColor: colors.border,
                    paddingBottom: 15,
                    marginHorizontal: -15,
                    paddingHorizontal: 15
                }}
            >
                <Text style={[FONTS.fontMedium, { color: colors.title, fontSize: 20 }]}>Short</Text>
                <TouchableOpacity
                    style={{ height: 38, width: 38, backgroundColor: colors.card, borderRadius: 38, alignItems: 'center', justifyContent: 'center' }}
                    onPress={() => shortRef.current.close()}
                >
                    <Image
                        style={{ width: 18, height: 18, resizeMode: 'contain', tintColor: colors.title }}
                        source={IMAGES.close}
                    />
                </TouchableOpacity>
            </View>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 5, marginTop: 20 }}>
                {GenderData.map((data, index) => {
                    return (
                        <TouchableOpacity
                            onPress={() => setActiveSize(data)}
                            key={index}
                            style={[{
                                backgroundColor: colors.background,
                                height: 34,
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: 30,
                                borderWidth: 1,
                                borderColor:theme.dark ? COLORS.white : colors.borderColor,
                                paddingHorizontal: 20,
                                paddingVertical: 5,
                                marginBottom: 5
                            }, activeSize === data && {
                                backgroundColor: colors.title,
                                borderColor: COLORS.primary,
                            }]}
                        >
                            <Text style={[{ ...FONTS.fontMedium, fontSize: 13, color: colors.title }, activeSize === data && { color:theme.dark ? COLORS.primary : COLORS.white }]}>{data}</Text>
                        </TouchableOpacity>
                    )
                })}
            </View>
        </View>
    )
}

export default ShortSheet2;