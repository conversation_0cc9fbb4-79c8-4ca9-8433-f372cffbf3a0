import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Image, Platform, } from 'react-native';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import { COLORS, FONTS } from '../../constants/theme';
import { useNavigation, useTheme } from '@react-navigation/native';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import Button from '../Button/Button';
import { ScrollView } from 'react-native-gesture-handler';
import { IMAGES } from '../../constants/Images';

type Props = {
  sheetRef :any
}

const FilterSheet2 = ({sheetRef} : Props) => {
  
  const theme = useTheme();
  const { colors } : {colors : any} = theme;

  const navigation = useNavigation<any>();

  const brandData = ["Adidas", "Reebok", "Zara", "Gucci", "Vogue"];

  const [activeSize, setActiveSize] = useState(brandData[0]);

  const categoriesData = ["All", "<PERSON>", "<PERSON>", "<PERSON>", "Dress", "Jack<PERSON>", "<PERSON><PERSON>", "<PERSON>"];

  const [active1Size, setActive1Size] = useState(categoriesData[0]);

  const sizeData = ["Small", "Medium", "Large", "XL", "2Xl"];

  const [active2Size, setActive2Size] = useState(sizeData[0]);

  const [multiSliderValue, setMultiSliderValue] = useState([200, 270])

  const multiSliderValuesChange = (values: any) => setMultiSliderValue(values)

  return (
      <View style={[GlobalStyleSheet.container, { paddingTop:Platform.OS === 'web' ?  15 : 0 }]}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottomWidth: 1,
            borderBottomColor: colors.border,
            paddingBottom: 15,
            marginHorizontal: -15,
            paddingHorizontal: 15
          }}
        >
          <Text style={[FONTS.fontMedium, { color: colors.title, fontSize: 20 }]}>Filters</Text>
          <TouchableOpacity
            style={{ height: 38, width: 38, backgroundColor: colors.card, borderRadius: 38, alignItems: 'center', justifyContent: 'center' }}
            onPress={() => sheetRef.current.close()}
          >
            <Image
              style={{ width: 18, height: 18, resizeMode: 'contain', tintColor: colors.title }}
              source={IMAGES.close}
            />
          </TouchableOpacity>
        </View>
        <ScrollView>
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: 10 }}>
              <Text style={{ ...FONTS.fontMedium, fontSize: 18, color: colors.title }}>Brand</Text>
              <TouchableOpacity
                  onPress={() => sheetRef.current.close()}
              >
                <Text style={{ ...FONTS.fontRegular, fontSize: 13, color: colors.title }}>See All</Text>
              </TouchableOpacity>
            </View>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 5, marginTop: 5 }}>
              {brandData.map((data, index) => {
                return (
                  <TouchableOpacity
                    onPress={() => setActiveSize(data)}
                    key={index}
                    style={[{
                      backgroundColor: colors.background,
                      height: 34,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: 30,
                      borderWidth: 1,
                      borderColor: theme.dark ? COLORS.white : colors.borderColor,
                      paddingHorizontal: 20,
                      paddingVertical: 5,
                      marginBottom: 5
                    }, activeSize === data && {
                      backgroundColor: colors.title,
                      borderColor: COLORS.primary,
                    }]}
                  >
                    <Text style={[{ ...FONTS.fontMedium, fontSize: 13, color: colors.title }, activeSize === data && {  color:theme.dark ? COLORS.primary : COLORS.white }]}>{data}</Text>
                  </TouchableOpacity>
                )
              })}
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: 10 }}>
              <Text style={{ ...FONTS.fontMedium, fontSize: 18, color: colors.title }}>Categories:</Text>
              <TouchableOpacity
                  onPress={() => sheetRef.current.close()}
              >
                <Text style={{ ...FONTS.fontRegular, fontSize: 13, color: colors.title }}>See All</Text>
              </TouchableOpacity>
            </View>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 5, marginTop: 5 }}>
              {categoriesData.map((data, index) => {
                return (
                  <TouchableOpacity
                    onPress={() => setActive1Size(data)}
                    key={index}
                    style={[{
                      backgroundColor: colors.background,
                      height: 34,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: 30,
                      borderWidth: 1,
                      borderColor: theme.dark ? COLORS.white : colors.borderColor,
                      paddingHorizontal: 20,
                      paddingVertical: 5,
                      marginBottom: 5
                    }, active1Size === data && {
                      backgroundColor: colors.title,
                      borderColor: COLORS.primary,
                    }]}
                  >
                    <Text style={[{ ...FONTS.fontMedium, fontSize: 13, color: colors.title }, active1Size === data && {  color:theme.dark ? COLORS.primary : COLORS.white }]}>{data}</Text>
                  </TouchableOpacity>
                )
              })}
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: 10 }}>
              <Text style={{ ...FONTS.fontMedium, fontSize: 18, color: colors.title }}>Size:</Text>
              <TouchableOpacity
                  onPress={() => sheetRef.current.close()}
              >
                <Text style={{ ...FONTS.fontRegular, fontSize: 13, color: colors.title }}>See All</Text>
              </TouchableOpacity>
            </View>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 5, marginTop: 5 }}>
              {sizeData.map((data, index) => {
                return (
                  <TouchableOpacity
                    onPress={() => setActive2Size(data)}
                    key={index}
                    style={[{
                      backgroundColor: colors.background,
                      height: 34,
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: 30,
                      borderWidth: 1,
                      borderColor: theme.dark ? COLORS.white : colors.borderColor,
                      paddingHorizontal: 20,
                      paddingVertical: 5,
                      marginBottom: 5
                    }, active2Size === data && {
                      backgroundColor: colors.title,
                      borderColor: COLORS.primary,
                    }]}
                  >
                    <Text style={[{ ...FONTS.fontMedium, fontSize: 13, color: colors.title }, active2Size === data && { color:theme.dark ? COLORS.primary : COLORS.white }]}>{data}</Text>
                  </TouchableOpacity>
                )
              })}
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: 10 }}>
              <Text style={{ ...FONTS.fontMedium, fontSize: 18, color: colors.title }}>Price:</Text>
            </View>
            <View style={{ marginTop: 5 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-around', marginBottom: 10 }}>
                <Text style={{ ...FONTS.fontMedium, fontSize: 12, color: colors.title, borderRadius: 20, backgroundColor: colors.card, textAlign: 'center', paddingVertical: 5, paddingHorizontal: 10 }}>${multiSliderValue[0]} </Text>
                <Text style={{ ...FONTS.fontMedium, fontSize: 12, color: colors.title, borderRadius: 20, backgroundColor: colors.card, textAlign: 'center', paddingVertical: 5, paddingHorizontal: 10 }}>${multiSliderValue[1]}</Text>
              </View>
              <MultiSlider
                values={[multiSliderValue[0], multiSliderValue[1]]}
                sliderLength={340}
                selectedStyle={{ backgroundColor: COLORS.title, }}
                containerStyle={{ alignSelf: 'center', marginTop: -10 }}
                onValuesChange={multiSliderValuesChange}
                markerStyle={{
                  ...Platform.select({
                    android: {
                      height: 24,
                      width: 24,
                      borderRadius: 50,
                      backgroundColor: COLORS.white,
                      borderWidth: 2,
                      borderColor: COLORS.title
                    }
                  })
                }}
                min={200}
                max={270}
                allowOverlap={false}
                minMarkerOverlapDistance={10}
              />

            </View>
            <View style={{ flexDirection: 'row', gap: 10, paddingRight: 10, marginTop: 20,marginBottom:50 }}>
              <View style={{ width: '50%' }}>
                <Button
                  onPress={() => sheetRef.current.close()}
                  btnRounded
                  title={"Reset"}
                  text={colors.title}
                  color={theme.dark ? 'rgba(255,255,255,.1)' : '#E8E2DB'}
                />
              </View>
              <View style={{ width: '50%' }}>
                <Button
                  onPress={() => sheetRef.current.close()}
                  btnRounded
                  title={"Apply"}
                  text={colors.card}
                />
              </View>
            </View>
        </ScrollView>
      </View>
  );
};

export default FilterSheet2;
