import { memo } from "react";
import { Text, View, Image } from "react-native";
import { FONTS } from "../../constants/theme";
import { IMAGES } from "../../constants/Images";

type HeaderStyle4Prop = {
  title?: string;
  canGoBack?: boolean;
};
const HeaderStyle4 = memo(
  ({
    title = "Please Provide a title",
    canGoBack = false,
  }: HeaderStyle4Prop) => {
    console.log(FONTS.fontJostLight);
    return (
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        {(canGoBack && (
          <Image source={IMAGES.arrowBack} style={{ width: 16, height: 16 }} />
        )) || <View />}
        <Text style={{ ...FONTS.fontMedium, ...FONTS.h3 }}>{title}</Text>
        <View />
      </View>
    );
  }
);

export default HeaderStyle4;
