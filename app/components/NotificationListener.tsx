import React, { useEffect } from 'react';
import NotificationService from '../services/NotificationService';
import { useSnackbar } from '../context/SnackbarContext';

const NotificationListener = () => {
  const { showSnackbar } = useSnackbar();

  useEffect(() => {
    // Subscribe to notifications
    const unsubscribe = NotificationService.onNotification((notification) => {
      console.log('Notification received in listener:', notification);
      
      // Determine notification type
      let type: 'success' | 'error' | 'info' | 'warning' = 'info';
      
      if (notification.data && notification.data.type) {
        switch (notification.data.type) {
          case 'login_success':
            type = 'success';
            break;
          case 'error':
            type = 'error';
            break;
          case 'warning':
            type = 'warning';
            break;
          default:
            type = 'info';
        }
      }
      
      // Show the notification as a snackbar
      showSnackbar(notification.title + ': ' + notification.body, type);
    });
    
    // Clean up subscription
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [showSnackbar]);
  
  // This component doesn't render anything
  return null;
};

export default NotificationListener;
