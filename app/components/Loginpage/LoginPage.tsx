import React, { useEffect, useState } from "react";
import { View, StyleSheet, ActivityIndicator } from "react-native";
import { CommonActions, useNavigation } from "@react-navigation/native";
import ShopifyWebView from "../ShopifyWebView";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { COLORS } from "../../constants/theme";

const LoginScreen = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkLoginStatus();
  }, []);

  const checkLoginStatus = async () => {
    try {
      const isLoggedIn = await AsyncStorage.getItem("isLoggedIn");
      const customerToken = await AsyncStorage.getItem("customerToken");

      if (isLoggedIn === "true" && customerToken) {
        // Check if company is verified
        const isCompanyVerified = await AsyncStorage.getItem("isCompanyVerified");

        if (isCompanyVerified === "true") {
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [{ name: "DrawerNavigation", params: { screen: "Home" } }],
            })
          );
        } else {
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [{ name: "CompanyRegistration" }],
            })
          );
        }
      } else {
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Error checking login status:", error);
      setIsLoading(false);
    }
  };

  const handleLoginSuccess = (userData: any) => {
    console.log("Login successful:", userData);

    // Show loading indicator to hide the WebView
    setIsLoading(true);

    // The ShopifyWebView component will handle the navigation directly
    // based on verification status
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ShopifyWebView onLoginSuccess={handleLoginSuccess} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default LoginScreen;
