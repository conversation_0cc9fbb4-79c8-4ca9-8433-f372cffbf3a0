import { View, Text } from "react-native";
import React, { useRef, useState } from "react";
import WebView from "react-native-webview";
import { ActivityIndicator } from "react-native-paper";

export default function CheckouWebViewPage({ route }: any) {
  const webViewRef = useRef<WebView>(null);
  const [checkoutLoader, setCheckoutLoader] = useState(true);

  const { checkoutUrl } = route?.params;
  console.log("checkoutUrl>>>", checkoutUrl);
  
  return (
    <>
      <WebView
        ref={webViewRef}
        source={{ uri: checkoutUrl }}
        // onNavigationStateChange={handleNavigationStateChange}
        // onMessage={handleMessage}
        onLoadStart={() => setCheckoutLoader(true)}
        onLoadEnd={() => setCheckoutLoader(false)}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        sharedCookiesEnabled={true}
        thirdPartyCookiesEnabled={true}
        startInLoadingState={true}
      />
    </>
  );
}
