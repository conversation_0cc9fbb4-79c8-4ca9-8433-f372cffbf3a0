import MultiSlider from "@ptomasroos/react-native-multi-slider";
import { useState } from "react";
import { View } from "react-native";
import { COLORS } from "../../constants/theme";

const PriceRangeSlider = ({ min, max, onChange }: any) => {
  const [values, setValues] = useState([min, max]);

  const handleChange = (newValues: number[]) => {
    setValues(newValues);
    onChange({ min: newValues[0], max: newValues[1] }); // sending to parent
  };

  return (
    <MultiSlider
      values={values}
      min={min}
      max={max}
      step={1}
      onValuesChange={handleChange}
      selectedStyle={{ backgroundColor: COLORS.lightgray, height: 4 }}
      unselectedStyle={{ backgroundColor: COLORS.lightgray, height: 4 }}
      // 🎯 THUMB (MARKER) STYLING
      markerStyle={{
        backgroundColor: COLORS.black,
        height: 20,
        width: 20,
        borderRadius: 10,
        borderWidth: 2,
        borderColor: "#fff",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 2,
        elevation: 3,
      }}
      // 🔧 CONTAINER STYLING
      containerStyle={{ marginTop: 20 }}
      trackStyle={{ height: 6, borderRadius: 3 }}
      sliderLength={150}
    />
  );
};
export default PriceRangeSlider;
