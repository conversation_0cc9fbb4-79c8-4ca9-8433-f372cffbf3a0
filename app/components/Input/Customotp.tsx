import React from 'react'
import { View, Text } from 'react-native';
import { useTheme } from '@react-navigation/native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import OTPTextInput from 'react-native-otp-textinput';

const Customotp = () => {

    const { colors } : {colors : any} = useTheme();

    return (
        <View>
            <OTPTextInput 
                tintColor={COLORS.primary}
                textInputStyle={{
                    borderBottomWidth : 2,
                    color :colors.title,
                }}
                containerStyle={{
                    width : 250,
                    marginVertical:0
                }}
                
            />
        </View>
    )
}

export default Customotp