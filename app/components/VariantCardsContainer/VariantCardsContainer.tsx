import { FlatList } from "react-native";
import VariantCard from "../VariantCard/VariantCard";
import { useEffect, useState } from "react";

export type Variant = {
  id: string;
  title: string;
  image: {
    src: string;
  };
  price: {
    amount: string;
    currencyCode: string;
  };
  availableForSale: boolean;
};

type VariantCardsContainerProp = {
  selectedVariantId?: string;
  variants: Variant[];
  handleVariantChange: (id: string) => void;
};

const VariantCardsContainer = ({
  selectedVariantId,
  variants,
  handleVariantChange,
}: VariantCardsContainerProp) => {
  useEffect(() => {
    if (!selectedVariantId) {
      handleVariantChange(variants[0].id);
    }
  }, []);
  const renderItem = ({ item }: { item: Variant }) => {
    // console.log("Variant Item: ", item);
    return (
      <VariantCard
        selectedVariantId={selectedVariantId}
        variant={item}
        handleVariantChange={handleVariantChange}
      />
    );
  };

  return (
    <FlatList
      data={variants}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      horizontal
      contentContainerStyle={{ gap: 8 }}
    />
  );
};

export default VariantCardsContainer;
