import React, { useEffect, useState } from "react";
import { Image, View, Dimensions, StyleSheet, Animated } from "react-native";
import Button from "../../components/Button/Button";

const { width } = Dimensions.get("window");

const AutoSwitchBanner = ({ banners, navigation }: any) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [blurOpacity] = useState(new Animated.Value(0));
  const [imageKey, setImageKey] = useState(Date.now()); // To force re-render image for effect

  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (currentIndex + 1) % banners.length;
      setCurrentIndex(nextIndex);
      setImageKey(Date.now());

      // Simulate brief blur
      blurOpacity.setValue(1);
      Animated.timing(blurOpacity, {
        toValue: 0,
        duration: 400, // smooth clear
        useNativeDriver: true,
      }).start();
    }, 3000);

    return () => clearInterval(interval);
  }, [currentIndex]);

  const currentBanner = banners[currentIndex];

  return (
    <View style={styles.container}>
      <View style={styles.imageWrapper}>
        <Image
          key={imageKey}
          source={{ uri: currentBanner?.imageUrl }}
          style={styles.image}
        />

        {/* Simulated blur overlay */}
        <Animated.View
          pointerEvents="none"
          style={[styles.blurOverlay, { opacity: blurOpacity }]}
        />

        <Button
          title="Shop Now"
          btnRounded
          size="sm"
          onPress={() => {
            navigation.navigate("ProductListingPage", {
              handle: currentBanner.handle,
            });
          }}
          style={styles.button}
          text="white"
        />
      </View>

      {/* Pagination Dots */}
      <View style={styles.paginationStyle}>
        {banners.map((_: any, index: any) => (
          <View
            key={index}
            style={[
              styles.dotStyle,
              currentIndex === index && styles.activeDotStyle,
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width,
    alignItems: "center",
    justifyContent: "center",
  },
  imageWrapper: {
    width: "90%",
    aspectRatio: 1,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
    overflow: "hidden",
  },
  image: {
    width: "100%",
    height: "100%",
    borderRadius: 15,
    objectFit: "cover",
  },
  blurOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(255,255,255,0.4)", // Simulated soft blur feel
    borderRadius: 15,
  },
  button: {
    position: "absolute",
    bottom: 30,
    left: 240,
    paddingVertical: 4,
    opacity: 0.9,
  },
  paginationStyle: {
    position: "absolute",
    bottom: 10,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  dotStyle: {
    width: 8,
    height: 8,
    backgroundColor: "rgb(255, 255, 255)",
    borderRadius: 10,
    marginHorizontal: 4,
  },
  activeDotStyle: {
    width: 25,
    height: 8,
    backgroundColor: "rgba(0, 0, 0, 1)",
    borderRadius: 10,
  },
});

export default AutoSwitchBanner;
