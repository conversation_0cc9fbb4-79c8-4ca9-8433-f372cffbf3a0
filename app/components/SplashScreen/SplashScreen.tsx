import React, { useEffect, useRef } from "react";
import { View, Image, Animated, StyleSheet, Dimensions } from "react-native";
import { useNavigation } from "@react-navigation/native";

const { width } = Dimensions.get("window");

export default function SplashScreen() {
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const logoScale = useRef(new Animated.Value(1.5)).current;

  // Controls slide left of logo + text container
  const containerTranslateX = useRef(new Animated.Value(0)).current;

  // Text fade in opacity
  const textOpacity = useRef(new Animated.Value(0)).current;

  // Text reveal sliding from right to left (starts fully shifted right)
  const textRevealTranslateX = useRef(new Animated.Value(144)).current;

  const navigation = useNavigation();

  useEffect(() => {
    Animated.sequence([
      // Step 1: Fade in and scale logo in center
      Animated.parallel([
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(logoScale, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]),

      Animated.delay(500),

      // Step 2: Fade in text (text opacity only)
      Animated.timing(textOpacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),

      Animated.delay(500),

      // Step 3: Slide container (logo + text) slightly left, and reveal text from right
      Animated.parallel([
        Animated.timing(containerTranslateX, {
          toValue: -width * 0.12, // small shift left
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(textRevealTranslateX, {
          toValue: 0, // text reveal from right to left
          duration: 500,
          useNativeDriver: true,
        }),
      ]),
      Animated.delay(1500),
    ]).start(() => {
      navigation.navigate("AuthLoading");
    });
  }, [
    logoOpacity,
    logoScale,
    containerTranslateX,
    textOpacity,
    textRevealTranslateX,
    navigation,
  ]);

  return (
    <View style={styles.container}>
      {/* hide the satus bar */}
      
      <Image
        source={require("../../../app/assets/icons/Splashscreenbg.png")}
        style={styles.background}
        resizeMode="cover"
      />

      {/* Single container for logo + text */}
      <Animated.View
        style={[
          styles.centeredContainer,
          {
            opacity: logoOpacity, // fade in logo and container
            transform: [
              { translateX: containerTranslateX }, // slide left
              { scale: logoScale }, // zoom out effect initially
            ],
          },
        ]}
      >
        {/* Logo */}
        <Image
          source={require("../../../app/assets/icons/SunriseLogo.png")}
          style={styles.logoIcon}
          resizeMode="contain"
        />

        {/* Text wrapper with fade and reveal animation */}
        <Animated.View
          style={[
            styles.textRevealWrapper,
            {
              opacity: textOpacity,
              overflow: "hidden",
            },
          ]}
        >
          <Animated.Image
            source={require("../../../app/assets/icons/SunriseText.png")}
            style={{
              width: 144,
              height: 48,
              transform: [{ translateX: textRevealTranslateX }],
            }}
            resizeMode="contain"
          />
        </Animated.View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  background: {
    width,
    height: "100%",
    position: "absolute",
  },
  centeredContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    top: 0,
    left: 130,
    right: 0,
    bottom: 0,
  },

  logoIcon: {
    width: 60,
    height: 60,
  },
  textRevealWrapper: {
    width: 144,
    height: 48,
    marginLeft: 10,
  },
});
