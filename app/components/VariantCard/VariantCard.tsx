import { Image, Text, TouchableOpacity, View } from "react-native";
import { Variant } from "../VariantCardsContainer/VariantCardsContainer";
import { COLORS, FONTS, FONTWEIGHT } from "../../constants/theme";

type SingleVariantCardProp = {
  selectedVariantId: string;
  variant: Variant;
  handleVariantChange: (id: string) => void;
};
const VariantCard = ({
  selectedVariantId,
  variant,
  handleVariantChange,
}: SingleVariantCardProp) => {
  return (
    <TouchableOpacity
      onPress={() => handleVariantChange(variant.id)}
      style={{
        alignItems: "center",
        // marginHorizontal: 5,
        // borderWidth: 0.1,
        borderColor: COLORS.gray,
        // padding: 10,
        borderRadius: 10,
        height: 100,
      }}
    >
      <Image
        source={{ uri: variant?.image.src }}
        style={{
          width: 80,
          height: 80,
          //   borderWidth: 1,
          borderWidth: selectedVariantId === variant?.id ? 1 : 0,
          aspectRatio: 1 / 1,
          objectFit: "contain",
          borderRadius: 15,
        }}
      />
      <View>
        <Text
          style={{
            ...FONTS.font,
            ...FONTWEIGHT.SemiBold,
          }}
        >
          {variant?.title}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export default VariantCard;
