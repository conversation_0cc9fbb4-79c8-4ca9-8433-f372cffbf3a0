import { View, TouchableOpacity } from "react-native";
import React from "react";
import { Checkbox, useTheme } from "react-native-paper";
import { COLORS, FONTS } from "../../constants/theme";

export default function CheckBox({ label, styles, checkedState }: any) {
  const theme = useTheme();
  const { colors }: { colors: any } = theme;

  return (
    <View style={{ ...styles }}>
      <View>
        <Checkbox.Item
          position="leading"
          label={label}
          labelStyle={{
            textAlign: "left",
            width: "100%",
            ...FONTS.fontRegular,
          }}
          style={{ paddingVertical: 5, padding: 0 }}
          uncheckedColor={colors.text}
          color={COLORS.primary}
          status={checkedState?.show ? "checked" : "unchecked"}
          onPress={checkedState?.checkedUnChecked}
        />
      </View>
    </View>
  );
}
