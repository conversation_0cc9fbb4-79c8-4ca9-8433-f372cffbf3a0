import { View, Text, ActivityIndicator, Image, Animated } from "react-native";
import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CommonActions, useNavigation } from "@react-navigation/native";
import { useTheme } from "@react-navigation/native";
import { IMAGES } from "@/app/constants/Images";
import { COLORS, FONTS, SIZES } from "@/app/constants/theme";
import Button from "@/app/components/Button/Button";

export default function ApplicationStatus() {
  const [isCompanyVerified, setIsCompanyVerified] = useState<boolean | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const navigation = useNavigation();
  const SHOPIFY_ADMIN_URL = process.env.EXPO_PUBLIC_ADMIN_API_URL;
  const SHOPIFY_ADMIN_TOKEN = process.env.EXPO_PUBLIC_ADMIN_ACCESS_TOKEN;

  useLayoutEffect(() => {
    const checkVerificationStatus = async () => {
      try {
        setIsLoading(true);
        const storedCustomerData = await AsyncStorage.getItem("customerData");
        const customerData = JSON.parse(storedCustomerData || "{}");
        const customerId = customerData?.data?.customer?.id;

        if (!customerId) {
          console.warn("Customer ID not found in AsyncStorage");
          setIsLoading(false);
          return;
        }

        const query = `
            query {
              customer(id: "${customerId}") {
                metafield(namespace: "custom", key: "verified") {
                  value
                }
              }
            }
          `;

        const response = await fetch(SHOPIFY_ADMIN_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": SHOPIFY_ADMIN_TOKEN,
          },
          body: JSON.stringify({ query }),
        });

        const result = await response.json();
        const verifiedStatus = result?.data?.customer?.metafield?.value;

        console.log("verify  ", typeof verifiedStatus);
        if (
          verifiedStatus == undefined ||
          verifiedStatus == null ||
          verifiedStatus == "" ||
          verifiedStatus == false
        ) {
          await AsyncStorage.setItem("isCompanyVerified", "false");
        }
        await AsyncStorage.setItem("isCompanyVerified", "true");
        setIsCompanyVerified(verifiedStatus === "true");
      } catch (error) {
        console.error("Verification check error:", error);
      } finally {
        setIsLoading(false);
      }
    };
    checkVerificationStatus();
  }, [navigation]);

  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      {isLoading ? (
        <ActivityIndicator size="small" />
      ) : (
        <CheckApplicationStatusApproved
          isCompanyVerified={isCompanyVerified === true}
        />
      )}
    </View>
  );
}

const CheckApplicationStatusApproved = ({
  isCompanyVerified,
}: {
  isCompanyVerified: boolean;
}) => {
  const navigation = useNavigation();
  const { colors } = useTheme();

  // Animation for the status icon
  const scaleAnim = useRef(new Animated.Value(0.5)).current;

  useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 5,
      tension: 40,
      useNativeDriver: true,
    }).start();
  }, []);

  return (
    <View
      style={{
        height: 450,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: isCompanyVerified
          ? "rgba(222, 255, 230, 0.5)"
          : "rgba(255, 247, 240, 0.5)",
        paddingHorizontal: 20,
      }}
    >
      <View
        style={{
          width: "100%",
          backgroundColor: colors.card,
          borderRadius: SIZES.radius_lg,
          padding: 24,
          alignItems: "center",
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 5,
          gap: 16,
        }}
      >
        <Animated.View
          style={{
            marginBottom: 10,
            transform: [{ scale: scaleAnim }],
          }}
        >
          {isCompanyVerified ? (
            <View
              style={{
                width: 80,
                height: 80,
                borderRadius: 40,
                backgroundColor: "rgba(0, 200, 83, 0.1)",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Image source={IMAGES.check} style={{ width: 40, height: 40 }} />
            </View>
          ) : (
            <View
              style={{
                width: 80,
                height: 80,
                borderRadius: 40,
                backgroundColor: "rgba(255, 180, 100, 0.1)",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Image
                source={{
                  uri: "https://static-00.iconduck.com/assets.00/pending-icon-512x504-9zrlrc78.png",
                }}
                style={{ width: 50, height: 50 }}
              />
            </View>
          )}
        </Animated.View>

        <Text
          style={{
            ...FONTS.h4,
            textAlign: "center",
            color: COLORS.title,
            marginBottom: 8,
          }}
        >
          {isCompanyVerified ? "Application Approved!" : "Application Pending"}
        </Text>

        <Text
          style={{
            ...FONTS.font,
            textAlign: "center",
            color: COLORS.title,
            marginBottom: 8,
          }}
        >
          Your application {isCompanyVerified ? "has" : "has not"} been
          approved, and{" "}
          {isCompanyVerified
            ? "your account is ready to use"
            : "is under review"}
        </Text>

        <Text
          style={{
            ...FONTS.fontSm,
            textAlign: "center",
            color: colors.text,
            marginBottom: 4,
          }}
        >
          If you have any queries or need to make changes to your application,
          contact us at:
        </Text>

        <Text
          style={{
            ...FONTS.fontSm,
            ...FONTS.fontMedium,
            textAlign: "center",
            color: COLORS.primary,
            marginBottom: 20,
          }}
        >
          <EMAIL>
        </Text>

        <View
          style={{
            width: "100%",
            gap: 12,
            marginTop: 8,
          }}
        >
          <Button
            title="Go to Home"
            btnRounded
            size="lg"
            color={COLORS.primary}
            onPress={() => {
              // @ts-ignore - Navigation typing issue

              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [
                    { name: "DrawerNavigation", params: { screen: "Home" } },
                  ],
                })
              );
            }}
          />
          <Button
            title="View Application"
            btnRounded
            outline
            size="lg"
            onPress={async () => {
              // @ts-ignore - Navigation typing issue
              await AsyncStorage.setItem("isCompanyFormSubmitted", "false");
              navigation.navigate("CompanyRegistration");
            }}
          />
        </View>
      </View>
    </View>
  );
};
