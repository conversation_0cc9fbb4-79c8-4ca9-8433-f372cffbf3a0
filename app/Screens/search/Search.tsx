import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  SafeAreaView,
  Image,
  TouchableOpacity,
  TextInput,
  Animated,
  Keyboard,
} from "react-native";
import { useFocusEffect, useTheme } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import { GlobalStyleSheet } from "../../constants/StyleSheet";
import { COLORS, FONTS, SIZES } from "../../constants/theme";
import { ScrollView } from "react-native-gesture-handler";
import { IMAGES } from "../../constants/Images";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList } from "../../Navigations/RootStackParamList";
import Header from "../../layout/Header";
import CardHeader from "../../components/CardHeader/CardHeader";
import CardSlider from "../../components/CardSlider/CardSlider";
import AddToCartCard from "../../components/AddToCartCard/AddToCartCard";
import SingleBannerImage, {
  SingleSmallBannerImage,
} from "../../components/SingleBannerImage/SingleBannerImage";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useLazyQuery, useQuery } from "@apollo/client";
import {
  MEDIA_IMAGE_BY_ID,
  RECENT_SEARCHES_PRODUCTS,
  SEARCH_QUERY,
} from "../../api/searchQuery";
import { ActivityIndicator } from "react-native-paper";
import "react-native-gesture-handler";

type SearchScreenProps = StackScreenProps<RootStackParamList, "Search">;

const Search = ({ navigation }: SearchScreenProps) => {
  const theme = useTheme();
  const { colors }: { colors: any } = theme;

  const [inputFocused, setInputFocused] = useState(false);

  // Animated values remain defined once on mount.
  const translateY = useRef(new Animated.Value(100)).current;
  const translateX = useRef(new Animated.Value(-50)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const [runSearchQuery, { data: searchResults, loading: searchLoading }] =
    useLazyQuery(SEARCH_QUERY);

  const searchProducts =
    searchResults?.search?.nodes?.map((item: any) => {
      return {
        title: item.title,
        handle: item.handle,
        vendor: item.vendor ?? "",
        image: item?.featuredImage?.url ?? null, // or item.featuredImage?.url if applicable
        price: { amount: item?.priceRange?.minVariantPrice?.amount },
        variants:
          item?.variants?.nodes?.map((variant: any) => ({
            title: variant?.title ?? "",
          })) ?? [],
      };
    }) ?? [];

  const { data, loading, error } = useQuery(RECENT_SEARCHES_PRODUCTS);
  const [fetchBannerImage, { data: bannerImageData }] =
    useLazyQuery(MEDIA_IMAGE_BY_ID);

  // Initialize recent searches states as empty arrays.
  const [items, setItems] = useState([]);
  const [show, setShow] = useState([]);
  const [search, setSearch] = useState("");
  const [startSearching, setStartSearching] = useState(false);
  const inputRef = useRef(null);
  const RECENT_SEARCHES_KEY = "recent_searches";

  useFocusEffect(
    React.useCallback(() => {
      // Run the animation and focus the input when the screen is focused.
      translateY.setValue(100);
      translateX.setValue(-50);
      opacity.setValue(0);

      Animated.parallel([
        Animated.timing(translateY, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(translateX, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Focus the input after animation finishes.
        inputRef.current?.focus();
      });
    }, []) // This effect will run whenever the screen is focused
  );

  // Single effect to load recent searches on mount.
  useEffect(() => {
    const loadRecentSearches = async () => {
      try {
        const stored = await AsyncStorage.getItem(RECENT_SEARCHES_KEY);
        const recent = stored ? JSON.parse(stored) : [];
        setItems(recent);
        setShow(recent);
      } catch (error) {
        console.error("Failed to load recent searches:", error);
      }
    };
    loadRecentSearches();
  }, []);

  // Removed duplicate useFocusEffect for recent searches.
  // Instead, only update if needed when search text changes.

  const getRecentSearches = async () => {
    try {
      const stored = await AsyncStorage.getItem(RECENT_SEARCHES_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error("Failed to load recent searches:", error);
      return [];
    }
  };

  const addSearchTerm = async (term: string, handle?: string) => {
    try {
      let current = await getRecentSearches();
      // Remove duplicates by title.
      current = current.filter((item: any) => item.title !== term);
      // Add term with handle and keep only the 4 most recent.
      const updated = [{ title: term, handle }, ...current].slice(0, 4);
      await AsyncStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(updated));
    } catch (error) {
      console.error("Failed to save search term:", error);
    }
  };

  const setSearchData = async (term: string, handle: string) => {
    setSearch(term); // Update search state with the term.
    await addSearchTerm(term, handle); // Save term to AsyncStorage.

    // Refresh local state.
    const updated = await getRecentSearches();
    setItems(updated);
    setShow(updated);
    setStartSearching(false);
  };

  // Debounced search query.
  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      if (search.trim()) {
        runSearchQuery({
          variables: {
            searchTerm: search.trim(),
            country: "IN",
            language: "EN",
          },
        });
      }
    }, 300);
    return () => clearTimeout(delayDebounce);
  }, [search]);

  const removeSearchTerm = async (termToRemove: { title: string }) => {
    const existing = await getRecentSearches();
    const updated = existing.filter(
      (term: any) =>
        term.title.toLowerCase().trim() !==
        termToRemove.title.toLowerCase().trim()
    );
    await AsyncStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(updated));
    setShow(updated);
  };

  const meta = data?.metaobjects?.nodes[0]?.fields;
  const addBannerGID =
    meta?.find((field: any) => field.key === "add_banner")?.value || "";

  useEffect(() => {
    if (addBannerGID) {
      fetchBannerImage({ variables: { id: addBannerGID } });
    }
  }, [addBannerGID]);

  // Section titles and references.
  const productSectionTitle =
    meta?.find((field: any) => field.key === "product_list_section_title")
      ?.value || "";
  const products =
    meta
      ?.find((field: any) => field.key === "select_products")
      ?.references?.nodes?.filter((node: any) => node.__typename === "Product") // make sure to extract only Products
      .map((product: any) => ({
        id: product.id,
        title: product.title,
        handle: product.handle,
        tags: product.tags,
        featuredImage: product.featuredImage,
        priceRange: product.priceRange,
        variants:
          product.variants?.nodes?.map((variant: any) => ({
            id: variant.id,
            title: variant.title,
            availableForSale: variant.availableForSale,
            selectedOptions: variant.selectedOptions,
            image: variant.image,
            price: variant.price,
            compareAtPrice: variant.compareAtPrice,
          })) || [],
      })) || [];

  const collectionSectionTitle =
    meta?.find((field: any) => field.key === "collection_list_title")?.value ||
    "";
  const collections =
    meta?.find((field: any) => field.key === "select_collections")?.references
      .nodes || [];

  const bannerImageUrl = bannerImageData?.node?.image?.url || "";
  const bannerHandle =
    meta?.find((field: any) => field.key === "banner_handle")?.reference
      ?.handle || "";

  return (
    <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
      <ScrollView keyboardShouldPersistTaps="handled">
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            marginTop: 12,
            gap: 12,
          }}
        >
          <View style={{ width: "10%", left: 5 }}>
            <Header leftIcon={"back"} rightIcon={"search"} />
          </View>
          <Animated.View
            style={{
              transform: [{ translateY }, { translateX }],
              opacity,
              height: 48,
              flex: 1,
              backgroundColor: colors.card,
              borderRadius: 28.58,
              marginRight: 16,
            }}
          >
            <TextInput
              ref={inputRef}
              style={{
                ...FONTS.fontRegular,
                fontSize: 16,
                color: colors.text,
                paddingLeft: 20,
                flex: 1,
              }}
              value={search}
              placeholder="Search"
              placeholderTextColor={COLORS.darkgray}
              onFocus={() => setInputFocused(true)}
              onChangeText={(text) => {
                setSearch(text);
                const trimmed = text.trim();
                setStartSearching(trimmed.length > 0);
                if (trimmed.length === 0) {
                  getRecentSearches().then(setShow);
                } else {
                  runSearchQuery({
                    variables: {
                      searchTerm: trimmed,
                      country: "IN",
                      language: "EN",
                    },
                  });
                }
              }}
              onSubmitEditing={async () => {
                if (search.trim()) {
                  setStartSearching(false);
                }
              }}
              cursorColor={COLORS.lightgray}
            />
          </Animated.View>
        </View>

        <View style={{ marginTop: 22,marginBottom:35 }}>
          {startSearching ? (
            <>
              <View>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                    paddingHorizontal: 13.5,
                  }}
                >
                  <Text
                    style={{
                      ...FONTS.fontSemiBold,
                      fontSize: SIZES.h4,
                      color: colors.title,
                    }}
                  >
                    Trending searches
                  </Text>
                </View>
                {searchLoading ? (
                  <View style={{ marginTop: 30 }}>
                    <ActivityIndicator size="small" color={COLORS.primary} />
                  </View>
                ) : (
                  <ScrollView
                    style={{
                      marginTop: 10,
                      paddingHorizontal: 13.5,
                      height: 130,
                    }}
                  >
                    {searchResults?.collections?.nodes.length > 0 ? (
                      searchResults.collections.nodes.map(
                        (data: any, index: any) => (
                          <TouchableOpacity
                            key={`collection-${index}`}
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                              justifyContent: "space-between",
                              paddingVertical: 5,
                            }}
                            onPress={() => {
                              setSearchData(data?.title, data?.handle);
                              navigation.navigate("ProductListingPage", {
                                handle: data?.handle,
                                searchQuery: data?.title,
                                headerBarTitle: data?.title,
                              });
                            }}
                          >
                            <View
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                                gap: 8,
                              }}
                            >
                              <Image
                                source={IMAGES.search}
                                style={{ width: 24, height: 24 }}
                              />
                              <Text
                                style={{
                                  ...FONTS.fontMedium,
                                  fontSize: SIZES.font,
                                  color: COLORS.lightgray,
                                }}
                              >
                                {data?.title}
                              </Text>
                            </View>
                            <Image
                              source={IMAGES.redirecticon}
                              style={{
                                height: 10,
                                width: 16,
                                resizeMode: "contain",
                                opacity: 0.5,
                                tintColor: colors.title,
                              }}
                            />
                          </TouchableOpacity>
                        )
                      )
                    ) : (
                      <Text style={{ ...FONTS.font }}>No results found </Text>
                    )}
                  </ScrollView>
                )}
                <View
                  style={{
                    marginTop: 40,
                    gap: 10,
                    paddingHorizontal: 13.5,
                  }}
                >
                  <CardHeader cardHeaderTitle="Top Products" />
                  {searchLoading ? (
                    <View style={{ marginTop: 30 }}>
                      <ActivityIndicator size="small" color={COLORS.primary} />
                    </View>
                  ) : searchResults?.search?.nodes?.length > 0 ? (
                    <AddToCartCard products={searchProducts} />
                  ) : (
                    <Text style={{ ...FONTS.font }}>No results found </Text>
                  )}
                </View>
              </View>
            </>
          ) : (
            <>
              <View>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                    marginHorizontal: 13.5,
                  }}
                >
                  <Text
                    style={{
                      ...FONTS.fontMedium,
                      fontSize: 20,
                      color: colors.title,
                    }}
                  >
                    RECENT SEARCHES
                  </Text>
                </View>

                <View style={{ marginTop: 10, marginHorizontal: 13.5 }}>
                  {show?.map((item: any, index: any) => (
                    <View
                      key={index}
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                        paddingVertical: 5,
                      }}
                    >
                      <TouchableOpacity
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          gap: 15,
                          width: "90%",
                        }}
                        onPress={() => {
                          setSearchData(item?.title, item?.handle);
                          navigation.navigate("ProductListingPage", {
                            handle: item?.handle,
                            searchQuery: item?.title,
                          });
                        }}
                      >
                        <Image
                          source={IMAGES.history}
                          style={{ width: 24, height: 24 }}
                        />
                        <Text
                          style={{
                            ...FONTS.fontMedium,
                            fontSize: SIZES.font,
                            color: COLORS.lightgray,
                          }}
                        >
                          {item?.title}
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        activeOpacity={0.5}
                        onPress={() => removeSearchTerm(item)}
                        style={{
                          width: 24,
                          height: 24,
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Image
                          style={{
                            height: 10,
                            width: 10,
                            resizeMode: "contain",
                            opacity: 0.5,
                            tintColor: colors.title,
                          }}
                          source={IMAGES.closesmall}
                        />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>

                {loading ? (
                  <View style={{ marginTop: 30 }}>
                    <ActivityIndicator size="large" color={COLORS.primary} />
                  </View>
                ) : (
                  <>
                    <View
                      style={{
                        marginTop: 40,
                        gap: 10,
                        marginHorizontal: 13.5,
                      }}
                    >
                      <CardHeader cardHeaderTitle={productSectionTitle} />
                      <AddToCartCard products={products} />
                    </View>
                    <View style={{ marginTop: 40 }}>
                      <View style={{ paddingHorizontal: 15 }}>
                        <CardHeader cardHeaderTitle={collectionSectionTitle} />
                      </View>
                      <View style={{ gap: 10, marginBottom: 19 }}>
                        <View style={{ paddingHorizontal: 15, marginTop: 10 }}>
                          <SingleBannerImage
                            badge={{ badgeTitle: "Badminton", bg: "white" }}
                            imgURL={bannerImageUrl}
                            imgStyles={{
                              width: 396,
                              height: 150,
                              borderRadius: 10,
                              objectFit: "cover",
                            }}
                            handle={bannerHandle}
                          />
                        </View>
                        <View>
                          <CardSlider
                            products={collections}
                            smallCard={true}
                            searchSuggectionCard={true}
                          />
                        </View>
                      </View>
                    </View>
                  </>
                )}
              </View>
            </>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Search;
