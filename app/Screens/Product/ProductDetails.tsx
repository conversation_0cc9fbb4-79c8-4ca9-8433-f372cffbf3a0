import React, { useState } from 'react'
import { View, Text, SafeAreaView, Image, TouchableOpacity } from 'react-native'
import { useTheme } from '@react-navigation/native'
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import Header from '../../layout/Header';
import {FONTS, COLORS } from '../../constants/theme';
import CheckoutItems from '../../components/CheckoutItems';
import SocialBtn from '../../components/Socials/SocialBtn';
import { ScrollView } from 'react-native-gesture-handler';
import Swiper from 'react-native-swiper/src';
import {FontAwesome} from "@expo/vector-icons";
import { IMAGES } from '../../constants/Images';
import { StackScreenProps } from '@react-navigation/stack';
import { RootStackParamList } from '../../Navigations/RootStackParamList';
import { useDispatch } from 'react-redux';
import { addToCart } from '../../redux/reducer/cartReducer';
import "react-native-gesture-handler";


const swiperimageData = [
    {
        image: IMAGES.itemDetails7,
    },
    {
        image: IMAGES.itemDetails8,
    },
    {
        image: IMAGES.itemDetails9,
    },
    {
        image: IMAGES.itemDetails10,
    },
]

type ProductDetailsScreenProps = StackScreenProps<RootStackParamList, 'ProductDetails'>;

const ProductDetails = ({ navigation } : ProductDetailsScreenProps) => {

    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    const productColors = ["#B45E58", "#5F75C5", "#C58F5E", "#919191", "#A872B1", "#699156"];

    const productSizes = ["S", "M", "L", "XL", "2XL"];

    const [activeColor, setActiveColor] = useState(productColors[0]);

    const [activeSize, setActiveSize] = useState(productSizes[0]);

    const dispatch = useDispatch();

    const addItemToCart = () => {
        dispatch(addToCart({
            id:"0",
            image:IMAGES.itemDetails7,
            title:"Men Black Grey Allover Printed\nRound Neck T-Shirt",
            price:"$270",
            discount:"$310",
            offer:"70% OFF",
            brand:"Jackets",
            // color:false, 
            // hascolor:true
        } as any ));
    }

    return (
        <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
            <Header
                title="Product Details"
                leftIcon={'back'}
                rightIcon={'cart'}
                onPress={() => {addItemToCart(); navigation.navigate('MyCart')}}
                //cartcount={14}
            />
            <ScrollView
                showsVerticalScrollIndicator={false}
            >
                <View style={[GlobalStyleSheet.container,{padding:0}]}>
                    <View style={{ zIndex: 12 }}>
                        <Swiper
                            autoplay
                            autoplayDelay={0.5}
                            autoplayLoop
                            height={'auto'}
                            showsButtons={false}
                            loop={false}
                            paginationStyle={{
                                bottom: 10,
                            }}
                            dotStyle={{
                                width: 8,
                                height: 8,
                                backgroundColor: 'rgba(0, 0, 0, 1)',
                                opacity: 0.5,
                                borderRadius: 10
                            }}
                            activeDotStyle={{
                                width: 30,
                                height: 8,
                                backgroundColor: 'rgba(0, 0, 0, 1)',
                                opacity: 0.5,
                                borderRadius: 10
                            }}
                        >
                            {swiperimageData.map((data:any, index:any) => {
                                return (
                                    <Image
                                        key={index}
                                        style={{ width: '100%', height: undefined, aspectRatio: 1 / 1, borderBottomRightRadius: 30, borderBottomLeftRadius: 30 }}
                                        source={data.image}
                                    />
                                )
                            })}
                        </Swiper>
                    </View>
                </View>
                <View style={[GlobalStyleSheet.container, { marginTop: 15 }]}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Text style={{ ...FONTS.fontRegular, fontSize: 14, color: colors.title }}>Jackets</Text>
                        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
                            <Image
                                style={{ height: 14, width: 14, resizeMode: 'contain' }}
                                source={IMAGES.star4}
                            />
                            <Text style={{ ...FONTS.fontSemiBold, fontSize: 14, color: colors.title }}>4.5<Text style={{ ...FONTS.fontRegular, fontSize: 14, color: theme.dark ? 'rgba(255,255,255,.5)' : 'rgba(0,0,0,.5)' }}> (490)</Text></Text>
                        </View>
                    </View>
                    <View style={{ marginTop: 10, marginBottom: 20 }}>
                        <Text style={{ ...FONTS.fontMedium, fontSize: 20, color: colors.title }}>Men Black Grey Allover Printed{"\n"}Round Neck T-Shirt</Text>
                    </View>
                    <View style={{ flexDirection: 'row', gap: 60, }}>
                        <View>
                            <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: colors.title }}>Price:</Text>
                            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 5, marginTop: 10 }}>
                                <Text style={{ ...FONTS.fontMedium, fontSize: 20, color: colors.title }}>$270</Text>
                                <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title, textDecorationLine: 'line-through' }}>$310</Text>
                            </View>
                        </View>
                        <View>
                            <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: colors.title }}>Quantity:</Text>
                            <View style={{ marginTop: 10 }}>
                                <CheckoutItems />
                            </View>
                        </View>
                    </View>
                    <View style={{ marginTop: 30 }}>
                        <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: colors.title }}>Items Size:</Text>
                        <View
                            style={{
                                flexDirection: 'row',
                                marginTop: 10
                            }}
                        >
                            {productSizes.map((data, index) => {
                                return (
                                    <TouchableOpacity
                                        onPress={() => setActiveSize(data)}
                                        key={index}
                                        style={[{
                                            height: 28,
                                            width: 28,
                                            borderRadius: 50,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            borderWidth: 1,
                                            borderColor: theme.dark ? COLORS.white : colors.borderColor,
                                            marginHorizontal: 4,
                                            backgroundColor: colors.card
                                        }, activeSize === data && {
                                            backgroundColor: COLORS.primary,
                                            borderColor: COLORS.primary,
                                        }]}
                                    >
                                        <Text style={[{ ...FONTS.fontSemiBold, fontSize: 12, color: colors.title }, activeSize === data && { color: COLORS.white }]}>{data}</Text>
                                    </TouchableOpacity>
                                )
                            })}
                        </View>
                    </View>
                    <View style={{ marginTop: 15 }}>
                        <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: colors.title }}>Items Color:</Text>
                        <View
                            style={{
                                flexDirection: 'row',
                                marginTop: 10
                            }}>
                            {productColors.map((data:any, index:any) => {
                                return (
                                    <TouchableOpacity
                                        onPress={() => setActiveColor(data)}
                                        key={index}
                                        style={{
                                            paddingHorizontal: 5,
                                            paddingVertical: 5,
                                            marginLeft: 4,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                    >
                                        {activeColor === data &&
                                            <View
                                                style={{
                                                    height: 33,
                                                    width: 33,
                                                    borderRadius: 30,
                                                    borderWidth: 2,
                                                    borderColor: theme.dark ? COLORS.white : COLORS.primary,
                                                    position: 'absolute',
                                                }}
                                            />
                                        }
                                        <View
                                            style={{
                                                height: 25,
                                                width: 25,
                                                borderRadius: 30,
                                                backgroundColor: data,
                                            }}
                                        />
                                    </TouchableOpacity>
                                )
                            })}
                        </View>
                    </View>
                    <View style={{ marginTop: 10 }}>
                        <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: colors.title }}>Description:</Text>
                        <View>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title, opacity: 0.7, marginTop: 10 }}>There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humor.</Text>
                        </View>
                    </View>
                </View>
            </ScrollView>
            <View style={{ height: 88, width: '100%', backgroundColor: colors.card, }}>
                <View style={[GlobalStyleSheet.container, { paddingHorizontal: 10, marginTop: 20, padding: 0 }]}>
                    <SocialBtn
                        icon={<FontAwesome name='shopping-cart' size={20} color={theme.dark ? COLORS.primary : COLORS.white} />}
                        rounded
                        color={theme.dark ? COLORS.white : COLORS.primary}
                        text={'Add To Cart'}
                        onPress={() => {addItemToCart(); navigation.navigate('MyCart')}}
                        textcolor={COLORS.white}
                    />
                </View>
            </View>
        </SafeAreaView>
    )
}

export default ProductDetails