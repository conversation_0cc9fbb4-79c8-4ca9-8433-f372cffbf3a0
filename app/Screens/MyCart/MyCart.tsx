import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Image,
  TouchableWithoutFeedback,
  Linking,
  TextInput,
  StyleSheet,
  Alert,
} from "react-native";
import { useTheme } from "@react-navigation/native";
import {
  ButtonLabel,
  COLORS,
  FONTS,
  FONTWEIGHT,
  SIZES,
} from "../../constants/theme";
import { ScrollView } from "react-native-gesture-handler";
import Button from "../../components/Button/Button";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList } from "../../Navigations/RootStackParamList";
import Header from "../../layout/Header";
import CheckBox from "../../components/CheckBox/CheckBox";
import { useMutation, useQuery } from "@apollo/client";
import { GET_CHECKOUT_PRODUCTS } from "../../api/checkoutProductsQuery";
import { ActivityIndicator } from "react-native-paper";
import {
  CREATE_CART,
  REMOVE_LINES_FROM_CART,
  UPDATE_LINES_IN_CART,
} from "../../api/cartQuery";
import AsyncStorage from "@react-native-async-storage/async-storage";
import _ from "lodash";
import Close from "../../assets/icons/closeicon.png";
import AddIcon from "../../assets/icons/addicon.png";
import RemoveIcon from "../../assets/icons/removeIcon.png";
import { IMAGES } from "../../constants/Images";
import { useDispatch, useSelector } from "react-redux";
import { setCartId } from "../../redux/reducer/cartReducer";
import "react-native-gesture-handler";
import WebView from "react-native-webview";
import { COMPANY_LOCATION_QUERY } from "@/app/api/companyDetailsQuery";
import { DRAFT_ORDER_MUTATION } from "@/app/api/draftOrder";

const filterCartData = async (data: any) => {
  if (!data)
    return {
      totalAmount: 0,
      subtoalAmount: 0,
      cartId: "",
      checkoutUrl: "",
      variants: [],
    };
  let filteredVariants = [];
  if (Array.isArray(data.lines.edges) && data.lines.edges.length > 0) {
    filteredVariants = data.lines.edges.map((variant, index) => {
      const size =
        variant.node?.attributes?.find(
          (attr: any) => attr.key.toLowerCase() === "size"
        )?.value || "";

      const filteredVariant = {
        lineId: variant.node.id,
        handle: variant.node?.merchandise?.product?.handle,
        price: variant.node?.merchandise?.price,
        merchandiseId: variant.node?.merchandise?.id,
        merchandiseTitle: variant.node?.merchandise?.title,
        productTitle: variant.node?.merchandise?.product?.title,
        quantity: variant.node?.quantity,
        url: variant.node?.merchandise?.product?.images?.edges[0]?.node?.url,
        size: size,
      };
      return filteredVariant;
    });
  }
  const cardData = {
    totalAmount: data.cost.totalAmount.amount,
    subtoalAmount: data.cost.subtotalAmount.amount,
    cartId: data.id,
    variants: filteredVariants,
    checkoutUrl: filteredVariants.length > 0 ? data.checkoutUrl : "",
  };
  return cardData;
};

const MyCart = ({ navigation, route }: any) => {
  const { cartId } = useSelector((state: any) => state.cart);

  const dispatch = useDispatch();
  const theme = useTheme();
  const [show, setshow] = useState(false);
  const { colors }: { colors: any } = theme;
  const cart: any = route?.params;
  const [merchandiseId, setMerchandiseId] = useState(null);
  const [cartUpdated, setCartUpdated] = useState(false);
  const [selectAll, setSelectAll] = useState(false);
  const [companyData, setCompanyData] = useState({});
  const [isVerified, setIsVerified] = useState(undefined);
  const [cartData, setCartData] = useState({
    totalAmount: 0,
    subtoalAmount: 0,
    cartId: "",
    variants: [],
    checkoutUrl: "",
  });

  useEffect(() => {
    const checkLogin = async () => {
      const checkVerificationStatus = async () => {
        try {
          const storedCustomerData = await AsyncStorage.getItem("customerData");
          const customerData = JSON.parse(storedCustomerData || "{}");
          const customerId = customerData?.data?.customer?.id;
          console.log("customerid>>>", customerId);

          if (!customerId) {
            console.warn("Customer ID not found in AsyncStorage");
            return;
          }

          const query = `
                query {
                  customer(id: "${customerId}") {
                    metafield(namespace: "custom", key: "verified") {
                      value
                    }
                  }
                }
              `;

          const response = await fetch(
            `${process.env.EXPO_PUBLIC_ADMIN_API_URL}`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": `${process.env.EXPO_PUBLIC_ADMIN_ACCESS_TOKEN}`,
              },
              body: JSON.stringify({ query }),
            }
          );
          const result = await response.json();
          const verifiedStatus = result?.data?.customer?.metafield?.value;

          console.log("Verification status:", result);
          const isVerified: any = verifiedStatus === "true";
          console.log("verifiedStatus::", verifiedStatus);

          if (
            verifiedStatus == undefined ||
            verifiedStatus == null ||
            verifiedStatus == "" ||
            verifiedStatus == false
          ) {
            await AsyncStorage.setItem("isCompanyVerified", "false");
          }
          await AsyncStorage.setItem("isCompanyVerified", "true");
          // Store verification status in AsyncStorage

          // Return the verification status instead of navigating directly
          setIsVerified(isVerified);

          return isVerified;
        } catch (error) {
          console.error("Verification check error:", error);
        }
      };
      const isCompanyVerified = await checkVerificationStatus();

      console.log("isCompanyVerified>>>>", isCompanyVerified);
    };
    checkLogin();
  }, []);
  const [isDraftOrderLoading, setIsDraftOrderLoading] = useState(false);

  const { loading, error, data } = useQuery(GET_CHECKOUT_PRODUCTS, {
    variables: { cartId },
  });

  const [createCart, { loading: cartLoading, error: cartError }] =
    useMutation(CREATE_CART);

  const [removeCartItem, { loading: removeLoading }] = useMutation(
    REMOVE_LINES_FROM_CART
  );

  const [updateItem, { loading: updateLoading }] =
    useMutation(UPDATE_LINES_IN_CART);

  const fetchCompanyLocation = async (customerId: string) => {
    const variables = {
      customerId: customerId,
    };

    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_ADMIN_API_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": process.env.EXPO_PUBLIC_ADMIN_ACCESS_TOKEN,
        },
        body: JSON.stringify({
          query: COMPANY_LOCATION_QUERY,
          variables,
        }),
      });

      console.log("response>>>>", response);
      const result = await response.json();
      const profiles = result.data?.customer?.companyContactProfiles || [];

      if (profiles.length > 0) {
        const company = profiles[0].company;
        const companyId = company?.id;
        const locationEdges = company?.locations?.edges || [];
        const locationIds = locationEdges.map((edge: any) => edge.node.id);

        setCompanyData((prev) => ({
          ...prev,
          companyId,
          locationIds,
        }));
        console.log("Company ID:", companyId);
        console.log("Location IDs:", locationIds);
      }
      console.log("result>>>>>", result);

      if (result.errors) {
        console.error("GraphQL Errors:", result.errors);
        return null;
      }

      return result.data;
    } catch (error) {
      console.error("Fetch Error:", error);
      return null;
    }
  };

  const fetchCustomerContacts = async (customerId: string) => {
    const variables = { customerId: customerId };

    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_ADMIN_API_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": process.env.EXPO_PUBLIC_ADMIN_ACCESS_TOKEN,
        },
        body: JSON.stringify({
          query: `
          query getCustomerContacts($customerId: ID!) {
            customer(id: $customerId) {
              companyContactProfiles {
                id
                isMainContact
                company {
                  id
                  name
                }
              }
            }
          }
        `,
          variables,
        }),
      });

      const result = await response.json();
      console.log("companyCOntat ifnormation>>>>>", result);

      const companyContactProfiles =
        result.data?.customer?.companyContactProfiles || [];
      const companyContactId =
        companyContactProfiles.length > 0 ? companyContactProfiles[0].id : null;

      console.log("Company Contact ID:", companyContactId);
      if (companyContactId) {
        setCompanyData((prev) => ({
          ...prev,
          companyContactId,
        }));
      }

      if (result.errors) {
        console.error("GraphQL Errors:", result.errors);
        return null;
      }

      const contacts = result.data?.customer?.companyContactProfiles || [];
      console.log("Customer Contacts >>>>", contacts);
      return contacts;
    } catch (error) {
      console.error("Fetch Error:", error);
      return null;
    }
  };

  useEffect(() => {
    const getCustomerId = async () => {
      const customer: any = await AsyncStorage.getItem("customerData");
      const customerData = JSON.parse(customer);
      console.log("id>>>>>", customerData?.data?.customer?.id);
      const customerId = customerData?.data?.customer?.id;
      console.log("id>>>", customerId);
      // if (id) {
      console.log("id>>>", customerId);
      fetchCompanyLocation(customerId);
      fetchCustomerContacts(customerId);
      // }
    };
    getCustomerId();
  }, [navigation]);

  useEffect(() => {
    if (route.params?.cartId && route.params.cartId !== merchandiseId) {
      setMerchandiseId(route.params.cartId);
      setCartUpdated(true);
    }
  }, [route.params?.cartId]);

  const toggleSelectAll = () => {
    if (cartData.variants?.length < 1) return;
    setSelectAll(!selectAll);
  };

  const handleIncrease = (variantId: string) => {
    if (cartLoading || removeLoading || updateLoading) return;

    const currentVariant = cartData.variants.find(
      (v: any) => v.merchandiseId === variantId
    );
    if (!currentVariant) return;

    const newQuantity = currentVariant.quantity + 1;

    updateItem({
      variables: {
        cartId,
        lines: [
          {
            id: currentVariant.lineId,
            merchandiseId: variantId,
            quantity: newQuantity,
          },
        ],
      },
    })
      .then(async (response) => {
        const filtered = await filterCartData(
          response.data.cartLinesUpdate.cart
        );
        setCartData(filtered);
      })
      .catch((error) => {
        console.error("Update error:", error);
      });
  };

  const handleDecrease = (variantId: string) => {
    if (cartLoading || removeLoading || updateLoading) return;

    const currentVariant = cartData.variants.find(
      (v: any) => v.merchandiseId === variantId
    );
    if (!currentVariant) return;

    const newQuantity = Math.max(1, currentVariant.quantity - 1);

    updateItem({
      variables: {
        cartId,
        lines: [
          {
            id: currentVariant.lineId,
            merchandiseId: variantId,
            quantity: newQuantity,
          },
        ],
      },
    })
      .then(async (response) => {
        const filtered = await filterCartData(
          response.data.cartLinesUpdate.cart
        );
        setCartData(filtered);
      })
      .catch((error) => {
        console.error("Update error:", error);
      });
  };

  const removeItem = async (lineId: any) => {
    const lineIds = [lineId];
    try {
      const response = await removeCartItem({
        variables: { cartId, lineIds },
      });
      const filtered = await filterCartData(response.data.cartLinesRemove.cart);
      setCartData(filtered);
      const newCartId = response?.data?.cartLinesRemove?.cart?.id;
      if (newCartId) {
        await AsyncStorage.setItem("merchandiseId", newCartId);
        setMerchandiseId(newCartId);
      }
    } catch (err) {
      console.error("Cart removal error:", err);
      alert("Something went wrong. Please try again.");
    }
  };

  const removeAllItems = () => {
    if (cartData.variants?.length < 1) return;
    createCart({
      variables: {
        lines: [],
      },
    })
      .then(async (response: any) => {
        if (response.data.cartCreate.userErrors.length > 0) {
          alert("Error: " + response.data.cartCreate.userErrors[0].message);
        } else {
          try {
            await AsyncStorage.removeItem("merchandiseId");
            setMerchandiseId("");
            dispatch(setCartId(""));
            setSelectAll(false);
            setCartData({
              totalAmount: 0,
              subtoalAmount: 0,
              cartId: "",
              variants: [],
              checkoutUrl: "",
            });
          } catch (e: any) {
            console.log("Error ", e?.message);
          }
        }
      })
      .catch((err) => {
        alert("Something went wrong. Please try again.");
        console.error("Cart clearing error:", err);
      });
  };

  useEffect(() => {
    const getFilteredData = async () => {
      const filtered = await filterCartData(data.cart);
      setCartData(filtered);
    };
    getFilteredData();
  }, [navigation, data]);

  const updateQuantity = (variantId: any, newQuantity: any) => {
    const currentVariant: any = cartData.variants.find(
      (v: any) => v.merchandiseId === variantId
    );
    if (!currentVariant) return;
    updateItem({
      variables: {
        cartId,
        lines: [
          {
            id: currentVariant.lineId,
            merchandiseId: variantId,
            quantity: newQuantity,
          },
        ],
      },
    })
      .then(async (response) => {
        const filtered = await filterCartData(
          response.data.cartLinesUpdate.cart
        );
        setCartData(filtered);
      })
      .catch((error) => {
        console.error("Update error:", error);
      });
  };

  const placeDraftOrder = async () => {
    const lineItems = cartData.variants.map((item: any) => ({
      variantId: item.merchandiseId, // adjust if your key differs
      quantity: item.quantity || 0,
    }));
    console.log("lineItems>>>", lineItems);

    const variables = {
      input: {
        lineItems,
        purchasingEntity: {
          purchasingCompany: {
            companyId: companyData?.companyId,
            companyLocationId: companyData?.locationIds[0],
            companyContactId: companyData?.companyContactId,
          },
        },
        paymentTerms: {
          paymentTermsTemplateId: "gid://shopify/PaymentTermsTemplate/5", // Net 60
          paymentSchedules: [{ issuedAt: new Date().toISOString() }],
        },
      },
    };

    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_ADMIN_API_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": process.env.EXPO_PUBLIC_ADMIN_ACCESS_TOKEN,
        },
        body: JSON.stringify({
          query: DRAFT_ORDER_MUTATION,
          variables,
        }),
      });

      const result = await response.json();
      console.log("Draft Order Result >>>>>", result);

      if (result.errors || result.data?.draftOrderCreate?.userErrors?.length) {
        console.error(
          "Draft Order Errors:",
          result.errors || result.data.draftOrderCreate.userErrors
        );
        return null;
      }

      const draftOrder = result.data?.draftOrderCreate?.draftOrder;
      console.log("Draft Order Created:", draftOrder.invoiceUrl);

      return draftOrder.invoiceUrl;
    } catch (error) {
      console.error("Draft Order Error:", error);
      return null;
    }
  };

  return (
    <SafeAreaView style={{ backgroundColor: COLORS.background, flex: 1 }}>
      <Header title="My Cart" leftIcon={"back"} titleLeft paddingLeft />
      <>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <CheckBox
            label={`${selectAll ? cartData.variants.length : 0}/${
              cartData.variants.length
            } items selected`}
            styles={{ width: "80%" }}
            checkedState={{
              show: selectAll,
              setshow: toggleSelectAll,
              checkedUnChecked: toggleSelectAll,
            }}
          />
          <TouchableOpacity
            onPress={removeAllItems}
            style={{ width: 40 }}
            disabled={cartData.variants.length === 0 || !selectAll}
          >
            <Image
              source={IMAGES.delete}
              style={{
                width: 20,
                height: 20,
                opacity: selectAll === false ? 0.3 : 1,
              }}
            />
          </TouchableOpacity>
        </View>
        <ScrollView
          contentContainerStyle={{
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {loading ? (
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                height: 600,
              }}
            >
              <ActivityIndicator size="large" color={COLORS.primary} />
            </View>
          ) : (
            cartData?.variants?.map((data: any, index: any) => {
              return (
                <TouchableOpacity
                  activeOpacity={0.7}
                  onPress={() => {
                    navigation.push("ProductDetailsPage", {
                      handle: data?.handle,
                    });
                  }}
                  style={[{ marginHorizontal: 5, width: "90%" }]}
                  key={index}
                >
                  <View
                    style={{
                      borderRadius: 15,
                      backgroundColor: COLORS.card,
                      flexDirection: "row",
                      marginVertical: 10,
                    }}
                  >
                    <View
                      style={{
                        width: "40%",
                        marginVertical: 15,
                        marginHorizontal: 5,
                      }}
                    >
                      <Image
                        style={{
                          width: "100%",
                          height: 140,
                          borderRadius: 20,
                          objectFit: "contain",
                        }}
                        source={{ uri: data?.url }}
                      />
                    </View>
                    <TouchableWithoutFeedback>
                      <View
                        style={{
                          marginTop: 20,
                          marginHorizontal: 5,
                          width: "60%",
                        }}
                      >
                        <View style={{ flexDirection: "row" }}>
                          <Text
                            style={{
                              width: "80%",
                              fontSize: SIZES.font,
                              color: COLORS.addToCartCardTitle,
                              ...FONTS.fontMedium,
                            }}
                          >
                            {data.productTitle}
                          </Text>
                          <TouchableOpacity
                            onPress={() => {
                              removeItem(data?.lineId);
                            }}
                          >
                            <Image
                              source={Close}
                              style={{
                                width: 20,
                                height: 20,
                                marginHorizontal: 12,
                                right: 15,
                              }}
                            />
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: "row",
                            gap: 5,
                            marginTop: 5,
                          }}
                        >
                          <Text style={{ ...FONTWEIGHT.Bold }}>
                            {"$" + data?.price.amount}
                          </Text>
                          <Text
                            style={{
                              color: COLORS.lightgray,
                              textDecorationLine: "line-through",
                              marginHorizontal: 2,
                              ...FONTWEIGHT.Normal,
                            }}
                          >
                            {"$" + data?.price.amount * 2}
                          </Text>
                        </View>
                        <View>
                          {data?.size && (
                            <View style={{ flexDirection: "row" }}>
                              <Text
                                style={{
                                  ...FONTS.fontSm,
                                  marginVertical: 3,
                                  color: COLORS.textBrandName,
                                  marginHorizontal: 2,
                                }}
                              >
                                Size
                              </Text>
                              <Text
                                style={{
                                  ...FONTS.fontSm,
                                  marginVertical: 3,
                                  color: COLORS.lightgray,
                                  marginHorizontal: 2,
                                  ...FONTWEIGHT.SemiBold,
                                }}
                              >
                                {data?.size}
                              </Text>
                            </View>
                          )}

                          <View style={{ flexDirection: "row" }}>
                            <Text
                              style={{
                                ...FONTS.fontSm,
                                color: COLORS.textBrandName,
                                marginHorizontal: 2,
                                marginBottom: 10,
                              }}
                            >
                              Color
                            </Text>
                            <Text
                              style={{
                                ...FONTS.fontSm,
                                color: COLORS.lightgray,
                                marginHorizontal: 2,
                                ...FONTWEIGHT.bold,
                              }}
                            >
                              {data?.merchandiseTitle}
                            </Text>
                          </View>
                        </View>
                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-around",
                            width: 100,
                            height: 30,
                            marginTop: 5,
                            borderRadius: 8,
                            alignItems: "center",
                          }}
                        >
                          <TouchableOpacity
                            style={styles.quantityButton}
                            hitSlop={{
                              top: 10,
                              bottom: 10,
                              left: 10,
                              right: 10,
                            }}
                            onPressOut={() =>
                              handleDecrease(data?.merchandiseId)
                            }
                            disabled={
                              updateLoading || removeLoading || cartLoading
                            }
                          >
                            <Image
                              style={{ width: 15, height: 15 }}
                              source={RemoveIcon}
                            />
                          </TouchableOpacity>

                          <QuantityInputBox
                            data={data}
                            updateQuantity={updateQuantity}
                          />

                          <TouchableOpacity
                            style={styles.quantityButton1}
                            hitSlop={{
                              top: 10,
                              bottom: 10,
                              left: 10,
                              right: 10,
                            }}
                            onPressOut={() =>
                              handleIncrease(data?.merchandiseId)
                            }
                            disabled={
                              updateLoading || removeLoading || cartLoading
                            }
                          >
                            <Image
                              style={{ width: 15, height: 15 }}
                              source={AddIcon}
                            />
                          </TouchableOpacity>
                        </View>
                      </View>
                    </TouchableWithoutFeedback>
                  </View>
                </TouchableOpacity>
              );
            })
          )}
        </ScrollView>
        <View
          style={{
            backgroundColor: COLORS.white,
            padding: 10,
            borderTopColor: COLORS.gray,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            marginHorizontal: 3,
            height: 150,
            gap: 10,
          }}
        >
          <View>
            <Text style={{ ...FONTS.font, ...FONTWEIGHT.Bold }}>
              Order Details
            </Text>
          </View>
          <View
            style={{
              width: "100%",
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text style={{ ...FONTS.fontSm, ...FONTWEIGHT.SemiBold }}>
              Subtotal
            </Text>
            <Text style={{ flexDirection: "row" }}>
              <Text
                style={{
                  ...FONTS.fontXLg,
                  ...FONTWEIGHT.Bold,
                  textAlign: "center",
                }}
              >
                $
              </Text>
              <Text
                style={{
                  ...FONTS.fontXLg,
                  textAlign: "center",
                  ...FONTS.fontSemiBold,
                }}
              >
                {cartData.subtoalAmount}
              </Text>
            </Text>
          </View>
          <View
            style={{
              width: "100%",
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <View
              style={{
                justifyContent: "center",
                gap: 5,
                width: "25%",
              }}
            >
              <Text style={{ flexDirection: "row" }}>
                <Text
                  style={{
                    ...FONTS.fontXLg,
                    ...FONTWEIGHT.Bold,
                    textAlign: "center",
                  }}
                >
                  $
                </Text>
                <Text
                  style={{
                    ...FONTS.fontXLg,
                    ...FONTWEIGHT.Bold,
                    textAlign: "center",
                  }}
                >
                  {cartData.subtoalAmount}
                </Text>
              </Text>
              <Text
                style={{
                  ...FONTS.fontXs,
                  ...FONTWEIGHT.SemiBold,
                  textAlign: "center",
                }}
              >
                {`(Incl. of all taxes)`}
              </Text>
            </View>
            <View>
              <Button
                rightDisabled={
                  cartData.checkoutUrl === "" 
                    ? true
                    : false
                }
                onPress={async () => {
                  if (cartData.checkoutUrl === "" || isVerified !== true) {
                    return Alert.alert(
                      "Your Company is not Verified Please Verify first then you can proceed"
                    );
                  }
                  setIsDraftOrderLoading(true);
                  // console.log(
                  //   "Ready for procced",
                  //   cartData.checkoutUrl === "" || isVerified !== true
                  // );
                  const draftOrderUrl = await placeDraftOrder();
                  setIsDraftOrderLoading(false);
                  console.log("cartData.checkoutUrl", draftOrderUrl);
                  navigation.navigate("CheckouWebViewPage", {
                    checkoutUrl: draftOrderUrl,
                  });
                }}
                title={ButtonLabel.placeOrder}
                btnRounded
                style={{ width: 200 }}
                loading={isDraftOrderLoading}
              />
            </View>
          </View>
        </View>
      </>
    </SafeAreaView>
  );
};

export default MyCart;

export const QuantityInputBox = ({ data, updateQuantity }: any) => {
  const [localQty, setLocalQty] = useState(String(data.quantity || 0));
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);
  const lastExternalQty = useRef(data.quantity);

  // Sync with external quantity changes ONLY if different and NOT caused by our own debounce update
  useEffect(() => {
    const newQtyStr = String(data.quantity || 0);
    if (
      data.quantity !== lastExternalQty.current && // new quantity from outside
      newQtyStr !== localQty
    ) {
      lastExternalQty.current = data.quantity;
      setLocalQty(newQtyStr);
    }
  }, [data.quantity]);

  const handleTextChange = (text: string) => {
    const numeric = text.replace(/[^0-9]/g, "");

    setLocalQty(numeric);

    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    if (numeric !== "") {
      debounceTimer.current = setTimeout(() => {
        const parsed = parseInt(numeric);
        if (!isNaN(parsed)) {
          lastExternalQty.current = parsed; // prevent flicker on response
          updateQuantity(data?.merchandiseId, parsed);
        }
      }, 500);
    }
  };

  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  return (
    <TextInput
      style={{
        fontSize: 16,
        width: 60,
        height: 31,
        textAlign: "center",
        borderWidth: 1,
        borderColor: "#ccc",
        paddingVertical: 2,
        paddingHorizontal: 4,
      }}
      keyboardType="numeric"
      value={localQty}
      onChangeText={handleTextChange}
      maxLength={7}
    />
  );
};

const styles = StyleSheet.create({
  quantityButton: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 6,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    borderRightWidth: 0,
    paddingTop: 10,
    alignItems: "center",
    // borderWidth:1
  },
  quantityButton1: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 6,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderLeftWidth: 0,
    paddingTop: 10,
    alignItems: "center",
    // borderWidth:1
  },
});
