import React, { useState, useRef } from 'react'
import { View, Text, SafeAreaView, TextInput, Image, TouchableOpacity } from 'react-native'
import { useTheme } from '@react-navigation/native'
import { COLORS, FONTS } from '../../constants/theme';
import { IconButton } from 'react-native-paper';
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import { ScrollView } from 'react-native-gesture-handler';
import CardStyle1 from '../../components/Card/CardStyle1';
import CardStyle3 from '../../components/Card/CardStyle3';
import { IMAGES } from '../../constants/Images';
import { StackScreenProps } from '@react-navigation/stack';
import { RootStackParamList } from '../../Navigations/RootStackParamList';
import BottomSheet2 from '../Shortcode/BottomSheet2';
import { useDispatch } from 'react-redux';
import { addTowishList } from '../../redux/reducer/wishListReducer';
import { addToCart } from '../../redux/reducer/cartReducer';
import "react-native-gesture-handler";


const sliderData = [
    {
        title: "Crazy Deals",
    },
    {
        title: "Budget Buys",
    },
    {
        title: "Best Offer",
    },
    {
        title: "Woman",
    },
    {
        title: "Dress",
    },
    {
        title: "unisex",
    },

]

const ListData = [
    {
        id:"4",
        image: IMAGES.item11,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        id:"5",
        image: IMAGES.item12,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        id:"6",
        image: IMAGES.item13,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        id:"7",
        image: IMAGES.item14,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        id:"8",
        image: IMAGES.product7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        id:"9",
        image: IMAGES.itemDetails7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        id:"10",
        image: IMAGES.item11,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        id:"11",
        image: IMAGES.item12,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        id:"12",
        image: IMAGES.item13,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        id:"13",
        image: IMAGES.item14,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        id:"14",
        image: IMAGES.product7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        id:"15",
        image: IMAGES.itemDetails7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
]

const gridData = [
    {
        id:"10",
        image: IMAGES.item11,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
    {
        id:"11",
        image: IMAGES.item12,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
    {
        id:"12",
        image: IMAGES.item13,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
    {
        id:"13",
        image: IMAGES.item14,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
    {
        id:"14",
        image: IMAGES.product7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
    {
        id:"15",
        image: IMAGES.itemDetails7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
    {
        id:"16",
        image: IMAGES.item11,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
    {
        id:"17",
        image: IMAGES.item12,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
    {
        id:"18",
        image: IMAGES.item13,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
    {
        id:"19",
        image: IMAGES.item14,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
    {
        id:"20",
        image: IMAGES.product7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
    {
        id:"21",
        image: IMAGES.itemDetails7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
        text: "FREE"
    },
]

type ProductsScreenProps = StackScreenProps<RootStackParamList, 'Products'>;

const Products = ({ navigation }: ProductsScreenProps) => {

    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    const [show, setshow] = useState(true);

    const sheetRef = useRef<any>();

    const dispatch = useDispatch();

    const addItemToWishList = (data: any) => {
        dispatch(addTowishList(data));
    }

    const addItemToCart = (data: any) => {
        dispatch(addToCart(data));
    }

    return (
        <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
            <View 
                style={[GlobalStyleSheet.container,{ 
                    height: 60, 
                    backgroundColor: theme.dark ? COLORS.primary : COLORS.secondary, 
                    flexDirection: 'row', 
                    alignItems: 'center',
                    padding:0 
                }]}
            >
                <IconButton
                    onPress={() => navigation.goBack()}
                    icon={props => <MaterialIcons name="arrow-back-ios" {...props} />}
                    iconColor={colors.title}
                    size={20}
                />
                <View style={{ height: 45, backgroundColor: colors.card, borderRadius: 10, marginLeft: -5, flex: 1 }}>
                    <TextInput
                        placeholder='Search Products'
                        placeholderTextColor={theme.dark ? 'rgba(255, 255, 255, .6)' : 'rgba(0, 0, 0, 0.6)'}
                        style={{ ...FONTS.fontRegular, fontSize: 16, color: colors.title, paddingLeft: 40,flex:1 }}
                    />
                    <View style={{ position: 'absolute', top: 12, left: 10 }}>
                        <Image
                            style={{ height: 20, width: 20, resizeMode: 'contain', tintColor: colors.title }}
                            source={IMAGES.search}
                        />
                    </View>
                </View>
                <TouchableOpacity
                    style={{ padding: 10, marginLeft: 10 }}
                    onPress={() => {
                        setshow(!show)
                    }}
                >
                    <Image
                        style={{ height: 22, width: 22, resizeMode: 'contain', tintColor: colors.title }}
                        source={
                            show
                                ?
                                IMAGES.list
                                :
                                IMAGES.grid
                        }
                    />
                </TouchableOpacity>
                <TouchableOpacity
                    style={{ padding: 10, marginRight: 10 }}
                    onPress={() => navigation.navigate('MyCart')}
                >
                    <Image style={{
                        height: 20,
                        width: 20,
                        resizeMode: 'contain',
                        tintColor: colors.title
                    }} source={IMAGES.shopping} />
                    <View style={[GlobalStyleSheet.notification, { position: 'absolute', right: 3, bottom: 22 }]}>
                        <Text style={{ ...FONTS.fontRegular, fontSize: 10, color: COLORS.white }}>14</Text>
                    </View>
                </TouchableOpacity>
            </View>
            <View style={[GlobalStyleSheet.container, { paddingTop: 10,flex:1 }]}>
                <View style={{ marginHorizontal: -15, marginBottom: 20 }}>
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={{ paddingHorizontal: 15}}
                    >
                        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10, justifyContent: 'center' }}>
                            {sliderData.map((data, index) => {
                                return (
                                    <TouchableOpacity
                                        key={index}
                                        style={{
                                            backgroundColor: colors.background,
                                            height: 34,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            borderRadius: 30,
                                            borderWidth: 1,
                                            borderColor: theme.dark ? COLORS.white : colors.borderColor,
                                            marginTop: 10,
                                            paddingHorizontal: 20,
                                            paddingVertical: 5
                                        }}>
                                        <Text style={{ ...FONTS.fontMedium, fontSize: 13, color: colors.title }}>{data.title}</Text>
                                    </TouchableOpacity>
                                )
                            })}
                        </View>
                    </ScrollView>
                </View>
                <View style={{ marginBottom: 20 }}>
                    <Image
                        style={{ height: 53, width: '100%' }}
                        source={IMAGES.ads3}
                    />
                </View>
                <View style={{ marginHorizontal: -15,flex:1 }}>
                    <ScrollView
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{ paddingBottom: 400,paddingHorizontal:15 }}
                    >
                        {show ?
                            <View style={[GlobalStyleSheet.row]}>
                                {ListData.map((data:any, index:any) => {
                                    return (
                                        <View key={index} style={[GlobalStyleSheet.col50, { marginBottom: 20 }]}>
                                            <CardStyle1
                                                id={data.id}
                                                key={index}
                                                image={data.image}
                                                title={data.title}
                                                price={data.price}
                                                discount={data.discount}
                                                review={data.review}
                                                closebtn
                                                onPress1={() => addItemToWishList(data)}
                                                onPress2={() =>{addItemToCart(data) ; navigation.navigate('MyCart')}}
                                                onPress={() => navigation.navigate('ProductDetails')}
                                            />
                                        </View>
                                    )
                                })}
                            </View>
                            :
                            <View style={{ marginTop: -10 }}>
                                {gridData.map((data, index) => {
                                    return (
                                        <CardStyle3
                                            id={data.id}
                                            key={index}
                                            title={data.title}
                                            price={data.price}
                                            image={data.image}
                                            discount={data.discount}
                                            review={data.review}
                                            grid
                                            text={data.text}
                                            onPress={() =>{addItemToCart(data) ; navigation.navigate('MyCart')}}
                                            onPress1={() => addItemToWishList(data)}
                                        />
                                    )
                                })}
                            </View>


                        }

                    </ScrollView>
                </View>
            </View>
            <View 
                style={[{ 
                    backgroundColor: colors.card,
                    height: 60,
                    width:'100%',
                    flexDirection: 'row',
                    position:'absolute',
                    bottom:0,
                    // padding:0
                }]}
            >
                <TouchableOpacity
                    style={{
                        flexDirection: 'row', alignItems: 'center', gap: 5,flex:1,
                        paddingHorizontal:10,
                        justifyContent:'center'
                    }}
                    onPress={() => sheetRef.current.openSheet('gender')}
                >
                    <Image
                        style={{ height: 20, width: 20, resizeMode: 'contain', tintColor: colors.title }}
                        source={IMAGES.user2}
                    />
                    <Text style={{ ...FONTS.fontMedium, fontSize: 15, color: colors.title }}>GENDER</Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={{
                        flexDirection: 'row', alignItems: 'center', gap: 5,flex:1,
                        paddingHorizontal:10,
                        justifyContent:'center'
                    }}
                    onPress={() => sheetRef.current.openSheet('short')}
                >
                    <Image
                        style={{ height: 20, width: 20, resizeMode: 'contain', tintColor: colors.title }}
                        source={IMAGES.arrowup}
                    />
                    <Text style={{ ...FONTS.fontMedium, fontSize: 15, color: colors.title }}>SORT</Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={{
                        flexDirection: 'row', alignItems: 'center', gap: 5,flex:1,
                        paddingHorizontal:10,
                        justifyContent:'center'
                    }}
                    onPress={() => sheetRef.current.openSheet('filter')}
                >
                    <Image
                        style={{ height: 20, width: 20, resizeMode: 'contain', tintColor: colors.title }}
                        source={IMAGES.filter}
                    />
                    <Text style={{ ...FONTS.fontMedium, fontSize: 15, color: colors.title }}>FILTER</Text>
                </TouchableOpacity>
            </View>
            <View style={{ width: 1, height: 32, backgroundColor: '#D9D9D9', position: 'absolute', left: 135, bottom: 15 }}></View>
            <View style={{ width: 1, height: 32, backgroundColor: '#D9D9D9', position: 'absolute', right: 135, bottom: 15 }}></View>
            <BottomSheet2
                ref={sheetRef}
            />
        </SafeAreaView>
    )
}

export default Products