import { useTheme } from "@react-navigation/native";
import React, { useRef, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Animated,
  Dimensions,
} from "react-native";
import Header from "../../layout/Header";
import { GlobalStyleSheet } from "../../constants/StyleSheet";
import {
  ButtonLabel,
  COLORS,
  FONTS,
  FONTWEIGHT,
  SIZES,
} from "../../constants/theme";
import { ScrollView } from "react-native-gesture-handler";
import { IMAGES } from "../../constants/Images";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList } from "../../Navigations/RootStackParamList";
import { transform } from "@babel/core";
import CardSlider, {
  CardSliderCatetgory,
} from "../../components/CardSlider/CardSlider";
import { useQuery } from "@apollo/client";
import { GET_MENU_ITEMS } from "../../api/categorypageQuery";
import Button from "../../components/Button/Button";
import { ActivityIndicator } from "react-native-paper";
import "react-native-gesture-handler";

type CategoryScreenProps = StackScreenProps<RootStackParamList, "Category">;
type AccordianProps = {
  navigation?: any;
  menuItems: any;
  styles?: any;
  accordianWithoutImg?: any;
  handle: any;
};
const Category = ({ navigation }: CategoryScreenProps) => {
  const { loading, error, data } = useQuery(GET_MENU_ITEMS);
  // if (loading) {
  //   return (
  //     <View
  //       style={{
  //         justifyContent: "center",
  //         alignItems: "center",
  //         height: 600,
  //       }}
  //     >
  //       <ActivityIndicator size="large" color={COLORS.primary} />
  //     </View>
  //   );
  // }
  if (error) {
    return (
      <View>
        <Text>{error.message}</Text>
      </View>
    );
  }
  const menus = data?.menu.items.map((menuItems: any) => {
    const title = menuItems.title;
    const redirectionUrl = menuItems.url;
    const allMenuItems = menuItems?.items.map((item: any) => {
      return item.items;
    });

    return [
      {
        title: title,
        redirectionUrl: redirectionUrl ? redirectionUrl : "",
        allMenuItems: allMenuItems,
      },
    ];
  });
  return (
    <SafeAreaView style={{ backgroundColor: COLORS.background, flex: 1 }}>
      <Header
        title={"Categories"}
        // rightIcon={"cart"}
        leftIcon={"back"}
        titleLeft
        paddingLeft
      />
      {loading ? (
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            height: 600,
          }}
        >
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      ) : (
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={{ gap: 10, marginTop: 16 }}>
            {menus?.map((menuItems: any, index: Number): any => {
              // console.log("menuItmes", menuItems);

              const itemHandle = menuItems[0]?.redirectionUrl
                ?.split("/")
                ?.pop();
              return (
                <>
                  <Accordian
                    key={index}
                    menuItems={menuItems}
                    navigation={navigation}
                    handle={itemHandle}
                  />
                </>
              );
            })}
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

export const Accordian = ({
  menuItems,
  navigation,
  styles,
  accordianWithoutImg = false,
  handle,
}: AccordianProps) => {
  const [isActive, setIsActive] = useState(false);
  const [activeTitle, setActiveTitle] = useState({});
  const animation = useRef(new Animated.Value(0)).current;

  const toggleAccordion = (title: any, redirectionUrl: any) => {
    if (isActive) {
      Animated.timing(animation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      const redirectionUrlHandle = redirectionUrl?.split("/")?.pop();
      setActiveTitle({ title, redirectionUrl: redirectionUrlHandle });
      Animated.timing(animation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
    setIsActive(!isActive);
  };

  const animatedHeight = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [
      0,
      menuItems[0].allMenuItems !== undefined &&
      menuItems[0].allMenuItems.length > 3
        ? 300
        : 200,
    ],
  });
  const SCREEN_WIDTH = Dimensions.get("screen").width;
  // console.log("menuItems[0]", menuItems[0].allMenuItems?.length);
  return (
    <View style={{ gap: 10 }}>
      <View>
        {menuItems?.map((menuItem: any, index: any) => {
          return (
            menuItems[0].allMenuItems?.length > 0 && (
              <TouchableOpacity
                key={menuItem.title + index}
                onPress={() =>
                  toggleAccordion(menuItem?.title, menuItem?.redirectionUrl)
                }
                style={
                  styles
                    ? { ...styles }
                    : {
                        // marginHorizontal: 16,
                        flexDirection: "row",
                        backgroundColor: COLORS.backgroundColor,
                        borderRadius: 8,
                        // borderWidth: 1,
                        width: "92%",
                        marginLeft: 16,
                      }
                }
              >
                <View style={{ flexDirection: "row" }}>
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      width: "55%",
                      marginLeft: 20,
                      // gap:5
                    }}
                  >
                    <Text
                      style={{
                        fontSize: SIZES.h4,
                        fontWeight: "500",
                        marginHorizontal: 10,
                        ...FONTS.fontTitle,
                      }}
                    >
                      {menuItem.title}
                    </Text>
                    <Image
                      source={{
                        uri: "https://cdn0.iconfinder.com/data/icons/leading-international-corporate-website-app-collec/16/Expand_menu-512.png",
                      }}
                      style={{
                        width: 15,
                        height: 15,

                        transform: [
                          {
                            rotateZ:
                              isActive && menuItems[0].allMenuItems?.length > 0
                                ? "180deg"
                                : "0deg",
                          },
                        ],
                        objectFit: "contain",
                      }}
                    />
                  </View>
                  {!accordianWithoutImg && (
                    <TouchableOpacity
                      onPress={() =>
                        navigation.navigate("ProductListingPage", { handle })
                      }
                    >
                      <Image
                        source={{
                          uri: "https://media.istockphoto.com/id/1192023529/photo/asian-badminton-player-is-hitting-in-court.jpg?s=612x612&w=0&k=20&c=32rDisHRvLTxaetdlFHZ0lsaWqu3yYO21w-hv4Z29xs=",
                        }}
                        style={{
                          width: SCREEN_WIDTH - 260,
                          height: 99,
                          borderTopRightRadius: 5,
                          borderBottomRightRadius: 5,
                          objectFit: "cover",
                        }}
                      />
                    </TouchableOpacity>
                  )}
                </View>
              </TouchableOpacity>
            )
          );
        })}
      </View>
      {menuItems[0].allMenuItems?.length > 0 && (
        <Animated.View
          style={{
            height: animatedHeight,
            overflow: "hidden",
            backgroundColor: COLORS.backgroundColor,
            marginHorizontal: 16,
            borderRadius: 8,
          }}
        >
          <ScrollView contentContainerStyle={{ width: "100%" }}>
            <View
              style={{
                flexDirection: "row",
                flexWrap: "wrap",
                width: "100%",
                gap: 10,
                marginHorizontal: 12,
                marginVertical: 10,
              }}
            >
              {menuItems[0]?.allMenuItems?.map((menuItem: any, index: any) => {
                return (
                  <View key={index}>
                    <CardSliderCatetgory
                      key={index}
                      products={menuItem}
                      smallCard={true}
                      // addBackground={true}
                      navigation={navigation}
                      categoryCards={true}
                    />
                  </View>
                );
              })}
            </View>
          </ScrollView>
          <Button
            title={`EXPLORE ALL ${activeTitle?.title?.toUpperCase()} ITEMS`}
            style={{ borderRadius: 0 }}
            onPress={() => {
              navigation.navigate("ProductListingPage", {
                handle: activeTitle?.redirectionUrl,
              });
            }}
          />
        </Animated.View>
      )}
    </View>
  );
};

export default Category;
