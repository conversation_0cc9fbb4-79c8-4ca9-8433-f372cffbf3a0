import React from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import ShopifyWebView from '../components/ShopifyWebView';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const LoginScreen = () => {
  const navigation = useNavigation();

  const handleLoginSuccess = async (userData: any) => {
    try {
      console.log('Login successful:', userData);
      
      // Store the token and user data
      await AsyncStorage.setItem('customerToken', userData.token);
      await AsyncStorage.setItem('tokenExpiresAt', userData.tokenExpiresAt);
      await AsyncStorage.setItem('customerData', JSON.stringify({
        customerId: userData.customerId,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName
      }));
      
      Alert.alert('Success', 'Login successful!');
      navigation.goBack();
    } catch (error) {
      console.error('Error storing login data:', error);
      Alert.alert('Error', 'Failed to store login information');
    }
  };

  return (
    <View style={styles.container}>
      <ShopifyWebView onLoginSuccess={handleLoginSuccess} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default LoginScreen; 