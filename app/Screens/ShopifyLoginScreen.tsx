import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import ShopifyWebView from '../components/ShopifyWebView';
import { useNavigation } from '@react-navigation/native';

const ShopifyLoginScreen = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(false);

  const handleLoginSuccess = (userData: any) => {
    setIsLoading(false);
    // Here you can store the user data in your app's state management
    // For example, using Redux or Context API
    console.log('Shopify login successful:', userData);
    
    // You can navigate to another screen or show a success message
    Alert.alert('Success', 'Login successful!');
  };

  return (
    <View style={styles.container}>
      <ShopifyWebView onLoginSuccess={handleLoginSuccess} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default ShopifyLoginScreen; 