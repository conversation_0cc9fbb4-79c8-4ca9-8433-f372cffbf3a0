import React, { useState } from 'react';
import { SafeAreaView, ScrollView, Text, TouchableOpacity, View, Platform } from 'react-native';
import { useTheme } from '@react-navigation/native';
import { Feather } from "@expo/vector-icons";
import Header from '../../layout/Header';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import { COLORS, FONTS } from '../../constants/theme';
import TaskModal from '../../components/Modal/TaskModal';

const TaskModalExample = () => {
  const { colors } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);

  const handleOkay = () => {
    console.log('Okay button pressed');
    setModalVisible(false);
  };

  return (
    <>
      <TaskModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onOkay={handleOkay}
        taskText="Are you sure you want to complete this task? This action cannot be undone."
        title="Complete Task"
      />

      <SafeAreaView style={{ flex: 1, backgroundColor: colors.card }}>
        <View style={{ backgroundColor: colors.background, flex: 1 }}>
          <View
            style={[{
              shadowColor: "#000",
              shadowOffset: {
                width: 2,
                height: 2,
              },
              shadowOpacity: .1,
              shadowRadius: 5,
            }, Platform.OS === "ios" && {
              backgroundColor: colors.card,
            }]}
          >
            <Header
              titleLeft
              title={'Task Modal Example'}
              leftIcon={'back'}
            />
          </View>
          <ScrollView>
            <View style={GlobalStyleSheet.container}>
              <View style={{
                backgroundColor: colors.card,
                borderRadius: 10,
                padding: 15,
                marginBottom: 15,
              }}>
                <Text style={{...FONTS.h6, color: colors.title, marginBottom: 8}}>
                  Modern Task Modal
                </Text>
                <Text style={{...FONTS.font, color: colors.text, marginBottom: 15}}>
                  A modern UI modal with task text and two buttons: Cancel and Okay.
                  The modal closes when you click outside of it.
                </Text>
                <TouchableOpacity
                  style={{
                    backgroundColor: COLORS.primary,
                    height: 45,
                    borderRadius: 8,
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'row',
                  }}
                  onPress={() => setModalVisible(true)}
                >
                  <Feather name="eye" size={18} color="#fff" style={{marginRight: 8}} />
                  <Text style={{...FONTS.font, color: '#fff'}}>Show Modal</Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        </View>
      </SafeAreaView>
    </>
  );
};

export default TaskModalExample;
