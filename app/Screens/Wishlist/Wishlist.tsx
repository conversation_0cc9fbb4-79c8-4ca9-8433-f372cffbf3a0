import React from 'react'
import { View, Text, TouchableOpacity, SafeAreaView, Platform } from 'react-native'
import { useTheme } from '@react-navigation/native'
import { COLORS, FONTS } from '../../constants/theme';
import { IconButton } from 'react-native-paper';
import {Feather} from "@expo/vector-icons";
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import { ScrollView } from 'react-native-gesture-handler';
import CardStyle1 from '../../components/Card/CardStyle1';
import { IMAGES } from '../../constants/Images';
import { StackScreenProps } from '@react-navigation/stack';
import { RootStackParamList } from '../../Navigations/RootStackParamList';
import { useDispatch, useSelector } from 'react-redux';
import { addToCart } from '../../redux/reducer/cartReducer';
import { removeFromwishList } from '../../redux/reducer/wishListReducer';
import "react-native-gesture-handler";

const sliderData = [
    {
        title: "All",
        active: true,
    },
    {
        title: "Child",
    },
    {
        title: "Man",
    },
    {
        title: "Woman",
    },
    {
        title: "Dress",
    },
    {
        title: "unisex",
    },

]

const CardStyle1Data = [
    {
        image: IMAGES.item11,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        image: IMAGES.item12,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        image: IMAGES.item13,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        image: IMAGES.item14,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        image: IMAGES.product7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        image: IMAGES.itemDetails7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        image: IMAGES.item11,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        image: IMAGES.item12,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        image: IMAGES.item13,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        image: IMAGES.item14,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        image: IMAGES.product7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
    {
        image: IMAGES.itemDetails7,
        title: "Bluebell Hand Block\nTiered Dress",
        price: "$80",
        discount: "$95",
        review: "(2k Review)",
    },
]

type WishlistScreenProps = StackScreenProps<RootStackParamList, 'Wishlist'>;

const Wishlist = ({ navigation } : WishlistScreenProps) => {

    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    const wishList = useSelector((state:any) => state.wishList.wishList);

    const dispatch = useDispatch();

    const addItemToCart = (data: any) => {
        dispatch(addToCart(data));
    }

    const removeItemFromWishList = (data: any) => {
        dispatch(removeFromwishList(data));
    }

    return (
        <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
            <View
                style={[{
                    shadowColor: "#000",
                    shadowOffset: {
                        width: 2,
                        height: 2,
                    },
                    shadowOpacity: .1,
                    shadowRadius: 5,
                }, Platform.OS === "ios" && {
                    backgroundColor: colors.card,
                }]}
            >
                <View
                    style={[{
                        height: 60,
                        flexDirection: 'row',
                        alignItems: 'center',
                        backgroundColor: colors.card,
                    }]}
                >
                    <View style={[GlobalStyleSheet.container, {
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingLeft: 20,
                        justifyContent: 'space-between'
                    }]}
                    >
                        <View style={{}}>
                            <Text style={{ ...FONTS.fontMedium, fontSize: 18, color: colors.title, textAlign: 'left' }}>Wishlist</Text>
                            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                                <Text style={{ ...FONTS.fontBold, fontSize: 12, color: colors.title }}>8<Text style={{ ...FONTS.fontRegular, fontSize: 12, color: colors.text }}> Items</Text></Text>
                                <View style={{ height: 4, width: 4, backgroundColor: colors.title, transform: [{ rotate: '45deg' }] }}></View>
                                <Text style={{ ...FONTS.fontRegular, fontSize: 12, color: colors.title }}>Total:<Text style={{ ...FONTS.fontBold }}> $213</Text></Text>
                            </View>
                        </View>
                        <View style={{}}>
                            <IconButton
                                onPress={() => navigation.navigate('Search')}
                                size={20}
                                iconColor={colors.title}
                                icon={props => <Feather  name="search" {...props} />}
                            />
                        </View>
                    </View>
                </View>
            </View>
            <View style={[GlobalStyleSheet.container,{flex:1}]}>
                <View style={{ marginHorizontal: -15, marginBottom: 20 }}>
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={{ paddingHorizontal: 15 }}
                    >
                        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10, justifyContent: 'center' }}>
                            {sliderData.map((data, index) => {
                                return (
                                    <TouchableOpacity
                                        key={index}
                                        style={{
                                            backgroundColor: data.active ? colors.title : colors.background,
                                            height: 34,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            borderRadius: 30,
                                            borderWidth: 1,
                                            borderColor: theme.dark ? COLORS.white : colors.borderColor,
                                            marginTop: 10,
                                            paddingHorizontal: 20,
                                            paddingVertical: 5
                                        }}>
                                        <Text style={{ ...FONTS.fontMedium, fontSize: 13, color: data.active ? colors.card : colors.title }}>{data.title}</Text>
                                    </TouchableOpacity>
                                )
                            })}
                        </View>
                    </ScrollView>
                </View>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingBottom: 130,flexGrow:1,justifyContent:wishList.length === 0 ? 'center' : 'flex-start',alignItems:wishList.length === 0 ? 'center' : 'flex-start' }}
                >
                    <View style={[GlobalStyleSheet.row]}>
                        {wishList.map((data:any, index:any) => {
                            return (
                                <View key={index} style={[GlobalStyleSheet.col50, { marginBottom: 20 }]}>
                                    <CardStyle1
                                        id={data.id}
                                        image={data.image}
                                        title={data.title}
                                        price={data.price}
                                        discount={data.discount}
                                        review={data.review}
                                        closebtn
                                        likebtn
                                        onPress1={() => removeItemFromWishList(data.id)}
                                        onPress2={() =>{addItemToCart(data) ; navigation.navigate('MyCart')}}
                                        onPress={() => navigation.navigate('ProductDetails')}
                                    />
                                </View>
                            )
                        })}
                        {wishList.length === 0 && 
                               <View
                                    style={{
                                        alignItems:'center',
                                        justifyContent:'center',
                                        marginTop:50
                                    }}
                                >
                                    <View
                                        style={{
                                            height:60,
                                            width:60,
                                            borderRadius:60,
                                            alignItems:'center',
                                            justifyContent:'center',
                                            backgroundColor:COLORS.primaryLight,
                                            marginBottom:20,
                                        }}
                                    >
                                        <Feather  color={COLORS.primary} size={24} name='heart'/>
                                    </View>
                                    <Text style={{...FONTS.h5,color:colors.title,marginBottom:8}}>Your Wishlist is Empty!</Text>    
                                    <Text
                                        style={{
                                            ...FONTS.fontSm,
                                            color:colors.text,
                                            textAlign:'center',
                                            paddingHorizontal:40,
                                            marginBottom:30,
                                        }}
                                    >Add Product to you favourite and shop now.</Text>
                                </View>
                            }
                    </View>
                </ScrollView>
            </View>
        </SafeAreaView>
    )
}

export default Wishlist