import { useTheme } from '@react-navigation/native';
import React from 'react';
import { View, Text, Image, TouchableOpacity, SafeAreaView } from 'react-native'
import Header from '../../layout/Header';
import {FONTS, COLORS } from '../../constants/theme';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import CardStyle3 from '../../components/Card/CardStyle3';
import { ScrollView } from 'react-native-gesture-handler';
import { IMAGES } from '../../constants/Images';
import "react-native-gesture-handler";

const TrackorderData = [
    {
        image: IMAGES.item13,
        title: "Bluebell Hand Block Tiered ",
        price: "$80",
        discount: "$95",
        btntitel: "Write Review",
    },

]

const Trackorder = () => {

    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    return (
        <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
            <Header
                title={"Track Order"}
                leftIcon={"back"}
                rightIcon3={"home"}
            />
            <ScrollView contentContainerStyle={{paddingBottom:100}}>
                <View style={GlobalStyleSheet.container}>
                    <View style={{
                        marginHorizontal: -15
                    }}>
                        {TrackorderData.map((data, index) => {
                            return (
                                <View key={index}>
                                    <CardStyle3
                                        id=''
                                        title={data.title}
                                        price={data.price}
                                        image={data.image}
                                        discount={data.discount} 
                                        removebtn
                                    />
                                </View>
                            )
                        })}
                    </View>
                    <View style={{ marginTop: 20, marginBottom: 20 }}>
                        <Text style={{ ...FONTS.fontMedium, fontSize: 18, color: colors.title }}>Track Order</Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 20 }}>
                        <Image
                            style={{ height: 24, width: 24, resizeMode: 'contain' }}
                            source={IMAGES.check}
                        />
                        <View>
                            <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: COLORS.success }}>Order Placed<Text style={{ ...FONTS.fontRegular, fontSize: 14, color: 'rgba(0, 0, 0, 0.50)' }}>  27 Dec 2023</Text></Text>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 14, color: colors.title }}>We have received your order</Text>
                        </View>
                        <View style={{ height: 70, width: 2, backgroundColor: COLORS.success, position: 'absolute', left: 11, top: 33 }}></View>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 20, marginTop: 40 }}>
                        <Image
                            style={{ height: 24, width: 24, resizeMode: 'contain' }}
                            source={IMAGES.check}
                        />
                        <View>
                            <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: COLORS.success }}>Order Confirm<Text style={{ ...FONTS.fontRegular, fontSize: 14, color: 'rgba(0, 0, 0, 0.50)' }}>  27 Dec 2023</Text></Text>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 14, color: colors.title }}>We has been confirmed</Text>
                        </View>
                        <View style={{ height: 60, width: 2, backgroundColor: COLORS.success, position: 'absolute', left: 11, top: 33 }}></View>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 20, marginTop: 40 }}>
                        <View style={{ height: 24, width: 24, borderWidth: 2, borderColor: colors.title, borderRadius: 24 }}>
                        </View>
                        <View>
                            <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: colors.title }}>Order Processed<Text style={{ ...FONTS.fontRegular, fontSize: 14, color: 'rgba(0, 0, 0, 0.50)' }}>  28 Dec 2023</Text></Text>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 14, color: colors.title }}>We are preparing your order</Text>
                        </View>
                        <View style={{ height: 60, width: 2, backgroundColor: colors.title, position: 'absolute', left: 11, top: 33 }}></View>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 20, marginTop: 40 }}>
                        <View style={{ height: 24, width: 24, borderWidth: 2, borderColor: colors.title, borderRadius: 24 }}>
                        </View>
                        <View>
                            <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: colors.title }}>Ready To Ship<Text style={{ ...FONTS.fontRegular, fontSize: 14, color: 'rgba(0, 0, 0, 0.50)' }}>  29 Dec 2023</Text></Text>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 14, color: colors.title }}>Your order is ready for shipping </Text>
                        </View>
                        <View style={{ height: 60, width: 2, backgroundColor: colors.title, position: 'absolute', left: 11, top: 33 }}></View>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 20, marginTop: 40 }}>
                        <View style={{ height: 24, width: 24, borderWidth: 2, borderColor: colors.title, borderRadius: 24 }}>
                        </View>
                        <View>
                            <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: colors.title }}>Out For Delivery<Text style={{ ...FONTS.fontRegular, fontSize: 14, color: 'rgba(0, 0, 0, 0.50)' }}>  31 Dec 2023</Text></Text>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 14, color: colors.title }}>Your order is out for delivery</Text>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </SafeAreaView>
    )
}

export default Trackorder