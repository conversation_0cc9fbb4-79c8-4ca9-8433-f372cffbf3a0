import React, { useRef, useState } from 'react';
import { useTheme } from '@react-navigation/native';
import { View, Text, SafeAreaView, Animated, TouchableOpacity, Image } from 'react-native';
import { COLORS, FONTS,  SIZES } from '../../constants/theme';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import Header from '../../layout/Header';
import { ScrollView } from 'react-native-gesture-handler';
import CardStyle3 from '../../components/Card/CardStyle3';
import { StackScreenProps } from '@react-navigation/stack';
import { RootStackParamList } from '../../Navigations/RootStackParamList';
import { IMAGES } from '../../constants/Images';
import "react-native-gesture-handler";


const MyorderData = [
    {
        image: IMAGES.item15,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Track Order",
    },
    {
        image: IMAGES.item19,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Track Order",
    },
    {
        image: IMAGES.item23,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Track Order",
    },
    {
        image: IMAGES.item24,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Track Order",
    },
    {
        image: IMAGES.product7,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Track Order",
    },
    {
        image: IMAGES.itemDetails7,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Track Order",
    },
]
const CompletedData = [
    {
        image: IMAGES.item11,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Write Review",
    },
    {
        image: IMAGES.item12,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Write Review",
    },
    {
        image: IMAGES.item13,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Write Review",
    },
    {
        image: IMAGES.item14,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Write Review",
    },
    {
        image: IMAGES.product7,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Write Review",
    },
    {
        image: IMAGES.itemDetails7,
        title: "Bluebell Hand Block Tiered",
        price: "$80",
        discount: "$95",
        btntitel: "Write Review",
    },
]

type MyorderScreenProps = StackScreenProps<RootStackParamList, 'Myorder'>;

const Myorder = ({ navigation } : MyorderScreenProps) => {


    const scrollRef = useRef<any>();
    const [currentIndex, setCurrentIndex] = useState(0);
    const scrollX = useRef(new Animated.Value(0)).current;

    const onPressTouch = (val : any) => {
        setCurrentIndex(val)
        scrollRef.current?.scrollTo({
            x: SIZES.width * val,
            animated: true,
        });
    }

    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    return (
        <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
            <Header
                title={"My Order"}
                leftIcon={'back'}
                rightIcon3={"home"}
            />
            <View style={{ flex: 1 }}>
                <View style={GlobalStyleSheet.container}>
                    <View style={{ flexDirection: 'row', gap: 10, marginRight: 10 }}>
                        <TouchableOpacity
                            onPress={() => onPressTouch(0)}
                            style={[GlobalStyleSheet.TouchableOpacity2, { backgroundColor: currentIndex === 0 ? colors.title : colors.background, borderColor: currentIndex === 0 ? colors.title : colors.title }]}
                        >
                            <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: currentIndex === 0 ? colors.card : colors.text }}>Ongoing</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => onPressTouch(1)}
                            style={[GlobalStyleSheet.TouchableOpacity2, { backgroundColor: currentIndex === 1 ? colors.title : colors.background, borderColor: currentIndex === 1 ? colors.title : colors.title }]}
                        >
                            <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: currentIndex === 1 ? colors.card : colors.text }}>Completed</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                <ScrollView
                    horizontal
                    pagingEnabled
                    showsHorizontalScrollIndicator={false}
                    ref={scrollRef}
                    onScroll={Animated.event(
                        [{ nativeEvent: { contentOffset: { x: scrollX } } }],
                        { useNativeDriver: false }
                    )}
                    onMomentumScrollEnd={(e:any) => {
                        if (e.nativeEvent.contentOffset.x.toFixed(0) == SIZES.width.toFixed(0)) {
                            setCurrentIndex(1)
                        } else if (e.nativeEvent.contentOffset.x.toFixed(0) == 0) {
                            setCurrentIndex(0)
                        } else {
                            setCurrentIndex(0)
                        }
                    }}
                >
                    <View style={{ width: SIZES.width }}>
                        <View style={[GlobalStyleSheet.container, { paddingTop: 0 }]}>
                            <View style={{ marginHorizontal: -15, }}>
                                <ScrollView
                                    showsVerticalScrollIndicator={false}
                                >
                                    {MyorderData.map((data, index) => {
                                        return (
                                            <CardStyle3
                                                id=''
                                                key={index}
                                                title={data.title}
                                                price={data.price}
                                                image={data.image}
                                                discount={data.discount}
                                                btntitel={data.btntitel}
                                                onPress={() => navigation.navigate('Trackorder')}
                                            />
                                        )
                                    })}
                                </ScrollView>
                            </View>
                        </View>
                    </View>
                    <View style={{ width: SIZES.width }}>
                        <View style={[GlobalStyleSheet.container, { paddingTop: 0 }]}>
                            <View style={{ marginHorizontal: -15, }}>
                                <ScrollView
                                    showsVerticalScrollIndicator={false}
                                >
                                    {CompletedData.map((data, index) => {
                                        return (
                                            <CardStyle3
                                                id=''
                                                key={index}
                                                title={data.title}
                                                price={data.price}
                                                image={data.image}
                                                discount={data.discount}
                                                btntitel={data.btntitel}
                                                onPress={() => navigation.navigate('WriteReview')}
                                            />
                                        )
                                    })}
                                </ScrollView>
                            </View>
                        </View>
                    </View>
                </ScrollView>
            </View>
        </SafeAreaView>
    )
}

export default Myorder;