import {
  CommonActions,
  useNavigation,
  useTheme,
} from "@react-navigation/native";
import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import {
  View,
  Text,
  SafeAreaView,
  Image,
  TouchableOpacity,
  SectionList,
  ScrollView,
  Platform,
  Button,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { GlobalStyleSheet } from "../../constants/StyleSheet";
import { FONTS, COLORS } from "../../constants/theme";

import ListItem from "../../components/list/ListItem";
import { IMAGES } from "../../constants/Images";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList } from "../../Navigations/RootStackParamList";
import "react-native-gesture-handler";
import AsyncStorage from "@react-native-async-storage/async-storage";
import WebView from "react-native-webview";
import { ActivityIndicator } from "react-native-paper";
import TaskModal from "@/app/components/Modal/TaskModal";

const btnData = [
  {
    title: "Your Order",
    navigate: "Myorder",
  },
  {
    title: "Wishlist",
    navigate: "Wishlist",
  },
  {
    title: "Coupons",
    navigate: "Coupons",
  },
  {
    title: "Track Order",
    navigate: "Trackorder",
  },
];

const ListwithiconData = [
  {
    title: "Account Settings",
    data: [
      {
        icon: IMAGES.user2,
        title: "Edit Profile",
        navigate: "EditProfile",
      },
      {
        icon: IMAGES.card2,
        title: "Saved Cards & Wallet",
        navigate: "Payment",
      },
      {
        icon: IMAGES.map2,
        title: "Saved Addresses",
        navigate: "SavedAddresses",
      },
      {
        icon: IMAGES.translation,
        title: "Select Language",
        navigate: "Language",
      },
      {
        icon: IMAGES.bell2,
        title: "Notifications Settings",
        navigate: "Notification",
      },
    ],
  },
  {
    title: "My Activity",
    data: [
      {
        icon: IMAGES.star,
        title: "Reviews",
        navigate: "WriteReview",
      },
      {
        icon: IMAGES.comment,
        title: "Questions & Answers",
        navigate: "Questions",
      },
    ],
  },
];

type ProfileScreenProps = StackScreenProps<RootStackParamList, "Profile">;

const Profile = ({ navigation }: ProfileScreenProps) => {
  const theme = useTheme();
  const { colors }: { colors: any } = theme;
  const webViewRef = useRef<WebView>(null);
  const [loading, setLoading] = useState(true); // Start with loading true
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [showWebView, setShowWebView] = useState(true);

  const uri = "https://sunrise-trade.myshopify.com/account/login";
  const [modalVisible, setModalVisible] = useState(false);
  // Handle initial load and subsequent navigation
  const handleLoadStart = () => {
    // Only show loading indicator for initial load or if explicitly needed
    if (!initialLoadComplete) {
      setLoading(true);
    }
  };

  const handleLoadEnd = () => {
    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    // Set a timeout to prevent flickering if another load starts soon
    loadingTimeoutRef.current = setTimeout(() => {
      setLoading(false);
      if (!initialLoadComplete) {
        setInitialLoadComplete(true);
      }
    }, 300); // Small delay to prevent flickering
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  const handleLogOut = async () => {
    try {
      const data = await AsyncStorage.removeItem("isLoggedIn");
      console.log("data", data);
      await AsyncStorage.removeItem("customerData");

      // await CookieManager.clearAll(); // ✅ Clears all WebView cookies

      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: "SignIn" }],
        })
      );
    } catch (error) {
      console.error("Logout error:", error);
    }
    // const data = await AsyncStorage.getItem("customerToken");
    // const customerdata = await AsyncStorage.getItem("customerdata");
  };

  const injectedDrawerClick = `
  (function openDrawerOnce() {
    if (window.__drawerOpened) return true; // Already opened once, skip

    const btn = document.querySelector('button[aria-label="Open main navigation"]');
    if (btn) {
      btn.click();
      window.__drawerOpened = true; // Mark that drawer is opened
    } else {
      setTimeout(openDrawerOnce, 500);
    }
  })();
  true;
`;

  const handleOkay = () => {
    setModalVisible(false);
  };
  return (
    <>
      {showWebView && (
        <WebView
          ref={webViewRef}
          source={{ uri: uri }}
          onNavigationStateChange={(navState) => {
            console.log("navState.url", navState.url);

            if (navState.url.includes("/pages/create-company")) {
              console.log("✅ Detected Registration Form URL");
              setShowWebView(false);
              // setLoading(true);
              // Hide the WebView
              navigation.navigate("ApplicationStatus");
              return;
            }

            if (navState.url.includes("/account/logout")) {
              console.log("🔓 Shopify logout initiated");

              // Hide WebView & show custom loader
              setLoading(true);
              webViewRef.current?.injectJavaScript(`
     const logoutModal = document.getElementById("Modal0");
      if (logoutModal) {
       logoutModal.remove();
    }
    document.body.style.overflow = 'hidden';
    true;
  `);
            }
            if (navState.url.includes("/account/profile")) {
              // Inject JS to simulate drawer open
              webViewRef.current?.injectJavaScript(injectedDrawerClick);
              webViewRef.current?.injectJavaScript(`
  (function() {
    const logoutSpan = Array.from(document.querySelectorAll('span'))
      .find(el => el.textContent === "Log out");

    if (logoutSpan) {
      const logoutButton = logoutSpan.closest("button");
      if (logoutButton) {
        logoutButton.addEventListener("click", function () {
          console.log("✅ Logout button clicked");
          window.ReactNativeWebView.postMessage(JSON.stringify({
            event: "custom_logout_clicked"
          }));

          const interval = setInterval(() => {
            const modal = document.getElementById("Modal0");
            if (modal) {
              modal.remove();
              clearInterval(interval);
            }
          }, 50);
        }, { once: true });
      } else {
        console.log("❌ Logout button not found");
      }
    } else {
      console.log("❌ Logout span not found");
    }
  })();
  true;
`);
            }

            if (
              navState.url.includes("/authentication/") &&
              navState.url.includes("/login")
            ) {
              console.log("✅ Shopify logout complete, now back to login page");

              // Clear all relevant user data
              AsyncStorage.multiRemove([
                "isLoggedIn",
                "customerData",
                "customerToken",
                "customerEmail",
                "accountNumber",
                "isCompanyVerified",
              ])
                .then(() => {
                  // Navigate to your SignIn screen
                  navigation.dispatch(
                    CommonActions.reset({
                      index: 0,
                      routes: [{ name: "ShopifyLogin" }],
                    })
                  );
                })
                .catch((err) => {
                  console.error("Logout cleanup error:", err);
                });
            }
          }}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          sharedCookiesEnabled={true}
          thirdPartyCookiesEnabled={true}
          startInLoadingState={true}
          onLoadStart={handleLoadStart}
          onLoadEnd={handleLoadEnd}
          style={{ flex: 1 }}
          onMessage={({ nativeEvent }: any) => {
            console.log("nativeEvent", nativeEvent);
            try {
              const data = JSON.parse(nativeEvent.data);
              if (data.event === "custom_logout_clicked") {
                console.log("🚪 Detected logout button click via injection");
                setLoading(true); // Your custom loader
              }
            } catch (err) {
              console.error("❌ Failed to parse WebView message:", err);
            }
          }}
          renderLoading={() => (
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <ActivityIndicator size="large" />
            </View>
          )}
        />
      )}
      {loading && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 9999,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "#fff",
          }}
        >
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      )}
      {/* <TaskModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onOkay={handleOkay}
        taskText="Your task description or confirmation message goes here."
        title="Your Modal Title" // Optional
      />
      <Button
        title="Check Verification Status"
        // onPress={() => setModalVisible(true)}
        onPress={() => navigation.navigate("ApplicationStatus")}
      /> */}
    </>
  );
};

export default Profile;
