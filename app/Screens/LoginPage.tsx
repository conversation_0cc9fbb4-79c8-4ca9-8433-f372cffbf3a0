import React, { useEffect, useState, useRef } from "react";
import {
  View,
  StyleSheet,
  ActivityIndicator,
  Button,
  Alert,
} from "react-native";
import ShopifyWebView from "../components/ShopifyWebView";
import { useNavigation } from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import client from "../api/appoloClient";
import { gql } from "@apollo/client";

const LoginPage = () => {
  const navigation = useNavigation();
  const [checkingLogin, setCheckingLogin] = useState(true);
  const alreadyNavigated = useRef(false);

  // Function to check if the user is logged in
  const checkIfLoggedIn = async () => {
    try {
      // Get the customer token and isLoggedIn flag from AsyncStorage
      const token = await AsyncStorage.getItem("customerToken");
      const isLoggedIn = await AsyncStorage.getItem("isLoggedIn");

      // If both token and isLoggedIn are true, navigate to CompanyPage
      if (token && isLoggedIn === "true") {
        const CUSTOMER_QUERY = gql`
          query {
            customer(customerAccessToken: "${token}") {
              id
            }
          }
        `;

        const result = await client.query({
          query: CUSTOMER_QUERY,
          context: {
            headers: {
              "X-Shopify-Customer-Access-Token": token,
            },
          },
          fetchPolicy: "network-only", // Avoid using cache
        });
        if (result.data.customer && !alreadyNavigated.current) {
          console.log("result.data.customer", result.data.customer);
          await AsyncStorage.setItem("customerdata", result.data);
          alreadyNavigated.current = true;
          navigation.reset({
            index: 0,
            routes: [{ name: "CompanyReagistration" }],
          });
          return;
        } else {
          await AsyncStorage.removeItem("customerToken");
          await AsyncStorage.removeItem("isLoggedIn");
        }
      }
    } catch (err) {
      console.warn("Login check error:", err);
    }

    setCheckingLogin(false); // Only show WebView if not logged in
  };

  useEffect(() => {
    checkIfLoggedIn();
  }, []);

  const handleLoginSuccess = (data: { accessToken: string }) => {
    console.log("✅ Login Success:", data);

    Alert.alert(
      "Login Successful",
      "You are now logged in.",
      [
        {
          text: "Continue",
          onPress: () => {
            navigation.navigate("CompanyRegistration");
          },
        },
      ],
      { cancelable: false }
    );
  };

  // Logout function
  const logout = async () => {
    const token = await AsyncStorage.getItem("customerToken");
    console.log("token>>>>>>>>>", token);
  };

  if (checkingLogin) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ShopifyWebView onLoginSuccess={handleLoginSuccess} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1 / 2,
  },
  loadingContainer: {
    flex: 1 / 2,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default LoginPage;
