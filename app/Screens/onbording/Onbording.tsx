import React, { useRef } from 'react';
import { useTheme } from '@react-navigation/native';
import { View, SafeAreaView, Text, Image, Animated, ScrollView, StyleSheet, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import Paragraph from '../../components/Paragraph';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import Button from '../../components/Button/Button';
import {Feather} from "@expo/vector-icons";
import { IMAGES } from '../../constants/Images';
import { StackScreenProps } from '@react-navigation/stack';
import { RootStackParamList } from '../../Navigations/RootStackParamList';
import "react-native-gesture-handler";


const swiperimageData = [
    {
        image: IMAGES.item1,
    },
    {
        image: IMAGES.item2,
    },
    {
        image: IMAGES.item3,
    },
]

const DATA = [
    {
        title: "Set Your Wardrobe With Our Amazing Selection !",
        desc: 'Change the quality of your appearance with pixio now.',
    },
    {
        title: "Your Ultimate Fashion Destination Awaits",
        desc: "Dive into a world of limitless possibilities."
    },
    {
        title: " Elevate Your Style, Anytime, Anywhere",
        desc: "Effortless sophistication for every occasion."
    },
]

type OnbordingScreenProps = StackScreenProps<RootStackParamList, 'Onbording'>;

const Onbording = ({ navigation } : OnbordingScreenProps) => {

    const theme = useTheme();
    const { colors } : {colors : any} = theme;

    const scrollRef = useRef<any>();

    const scrollX = useRef(new Animated.Value(0)).current;

   const onScroll = (val:any) => {
        scrollRef.current?.scrollTo({
            x: val.nativeEvent.contentOffset.x,
            animated: true,
        });
    }

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.background, }}>
            <ScrollView contentContainerStyle={{ flexGrow:1 }}>
                <View style={[GlobalStyleSheet.container,{padding:0, flex: 1 }, Platform.OS === 'web' && {overflow:'hidden'}]}>
                    <LinearGradient colors={['#FFB6BF', '#FF9596']}
                        style={[{
                            width: '60%', height: null,aspectRatio:1/1.6, borderBottomRightRadius: 60, borderTopRightRadius: 60, marginTop: 95, zIndex: 9
                        },Platform.OS === "ios" && {
                            marginTop:40,
                            aspectRatio:1/1.8
                        }]}
                    >
                    </LinearGradient>
                    <View style={[{
                        width: '60%',
                        height: null,
                        aspectRatio:1/2.1,
                        backgroundColor: COLORS.secondary,
                        position: 'absolute',
                        right: 0,
                        borderBottomLeftRadius: 160
                    },Platform.OS === "ios" && {
                        aspectRatio:1/2.1
                    }]}></View>
                    <View 
                        style={[{ 
                        position: 'absolute', 
                        top: 160, 
                        width: 600, 
                        transform: [{ rotate: '90deg' }], left: -20, right: 0 
                        },Platform.OS === "ios" && {
                            left:-10
                        }]}
                    >
                        <Paragraph
                            fontSize={Platform.OS === "ios" ? 80 : 90}
                            fill={"#FEEB9D"}
                            title={'FASHION'}
                            stroke={COLORS.primary}
                        />
                    </View>
                    <View style={[{ 
                        position: 'absolute', 
                        zIndex: 9, top:27, left: -30, },
                        Platform.OS === "ios" && {
                            top:27
                        }

                    ]}>

                        <ScrollView
                            scrollEnabled={false}
                            horizontal
                            pagingEnabled
                            ref={scrollRef}
                            scrollEventThrottle={16}
                            decelerationRate="fast"
                            showsHorizontalScrollIndicator={false}
                            onScroll={Animated.event(
                                [{ nativeEvent: { contentOffset: { x: scrollX } } }],
                                { useNativeDriver: false },
                            )}
                        >
                            {swiperimageData.map((data, index) => (
                                <View style={styles.slideItem2} key={index}>
                                    <Image
                                        key={index}
                                        style={[{ width:'70%', height:null,aspectRatio:2/3.57, resizeMode: 'contain' },Platform.OS === "ios" && {
                                            aspectRatio:2/3.5
                                        }]}
                                        source={data.image}
                                    />
                                </View>

                            ))}
                        </ScrollView>
                    </View>
                </View>
                <View style={{ marginTop: 30 }}>
                    <ScrollView
                        // contentContainerStyle={{ marginTop: 20 }}
                        horizontal
                        pagingEnabled
                        scrollEventThrottle={16}
                        decelerationRate="fast"
                        showsHorizontalScrollIndicator={false}
                        onScroll={onScroll}>
                        {DATA.map((data, index) => (

                            <View style={[styles.slideItem,Platform.OS === "ios" && {
                                paddingBottom:35
                            }]} key={index}>
                                <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                                    <Text style={{ ...FONTS.fontMedium, ...FONTS.h3, textAlign: 'center', color: colors.title }}>{data.title}</Text>
                                    <Text style={{ ...FONTS.fontRegular, fontSize: 18, textAlign: 'center', lineHeight: 24, paddingHorizontal: 25, color: colors.title, paddingTop: 10 }}>{data.desc}</Text>
                                </View>
                            </View>

                        ))}
                    </ScrollView>
                    <View style={[styles.indicatorConatiner,Platform.OS === "ios" && { 
                        bottom:10
                    }]} pointerEvents="none">
                        {DATA.map((x, i) => (
                            <Indicator i={i} key={i} scrollValue={scrollX} />
                        ))}
                    </View>
                </View>
                <View style={GlobalStyleSheet.container}>
                    <View style={{ paddingHorizontal: 20, paddingBottom: 5, paddingTop: 0 }}>
                        <Button
                            title={'Get Started'}
                            btnRounded
                            fullWidth
                            icon={<Feather  size={24} color={colors.title} name={'arrow-right'} />}
                            onPress={() => navigation.navigate('SignIn')}
                            color={colors.title}
                        />
                    </View>
                </View>
            </ScrollView>
        // </SafeAreaView>
    )
}

function Indicator({ i, scrollValue } : any) {

    const { colors } : {colors : any} = useTheme();

    const translateX = scrollValue.interpolate({
        inputRange: [-SIZES.width + i * SIZES.width, i * SIZES.width, SIZES.width + i * SIZES.width],
        outputRange: [-20, 0, 20],
    });
    return (
        <View style={[styles.indicator, { backgroundColor: colors.background, borderColor: colors.title }]}>
            <Animated.View
                style={[styles.activeIndicator, { transform: [{ translateX }], backgroundColor: colors.title }]}
            />
        </View>
    );
}


const styles = StyleSheet.create({
    slideItem: {
        width: SIZES.width,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
        paddingBottom: 60,
    },
    slideItem2: {
        width: SIZES.width > SIZES.container ? SIZES.container: SIZES.width,
    },

    indicatorConatiner: {
        alignSelf: 'center',
        position: 'absolute',
        bottom: 20,
        flexDirection: 'row',
    },
    indicator: {
        height: 10,
        width: 10,
        borderRadius: 5,
        marginHorizontal: 5,
        borderWidth: 1,
        overflow: 'hidden',
    },
    activeIndicator: {
        height: '100%',
        width: '100%',
        backgroundColor: COLORS.primary,
        borderRadius: 10,
    },

})
export default Onbording;