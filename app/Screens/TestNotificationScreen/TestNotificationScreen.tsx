import React from 'react';
import { SafeAreaView, ScrollView, View, Text } from 'react-native';
import { useTheme } from '@react-navigation/native';
import Header from '../../layout/Header';
import TestNotification from '../../components/TestNotification/TestNotification';
import { FONTS } from '../../constants/theme';

const TestNotificationScreen = () => {
  const theme = useTheme();
  const { colors } = theme;

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <Header
        title="Notification Tester"
        leftIcon={'back'}
      />
      <ScrollView>
        <View style={{ padding: 15 }}>
          <Text style={{ 
            ...FONTS.fontMedium, 
            fontSize: 16, 
            color: colors.text, 
            marginBottom: 15,
            textAlign: 'center'
          }}>
            Use this screen to test and troubleshoot push notifications
          </Text>
          
          <TestNotification />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default TestNotificationScreen;
