import {
  View,
  Text,
  ScrollView,
  Dimensions,
  Image,
  TouchableOpacity,
  StyleSheet,
  Animated,
  FlatList,
} from "react-native";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import Header from "../../layout/Header";
import {
  ButtonLabel,
  COLORS,
  FONTS,
  FONTWEIGHT,
  SIZES,
} from "../../constants/theme";
import { useQuery } from "@apollo/client";
import { GET_CATEGORY_PRODUCTS } from "../../api/viewCategoryProductQuery";

import SingleProductCard from "../../components/SingleProductCard/SingleProductCard";
import BottomNavModal from "../../components/BottomSheetNew/BottomSheetNew";
import {
  ActivityIndicator,
  Checkbox,
  RadioButton,
  useTheme,
} from "react-native-paper";
import CheckBox from "../../components/CheckBox/CheckBox";
import { GET_COLLECTION_PRODUCTS } from "../../api/homepageQuery";
import { IMAGES } from "../../constants/Images";
import PriceRangeSlider from "../../components/PriceRangeSlider/PriceRangeSlider";
import "react-native-gesture-handler";

export default function ProductListingPage({ navigation, route }: any) {
  const { handle, searchQuery, headerBarTitle } = route?.params;
  const [modalVisible, setModalVisible] = useState(false);
  const [filterSortOptions, setFilterSortOptions] = useState(false);
  const [selectedValue, setSelectedValue] = useState("");
  const [productList, setProductList] = useState([]);
  const sortOptions = [
    { label: "BEST SELLING", sortKey: "BEST_SELLING", reverse: false },
    { label: "NEWEST ARRIVALS", sortKey: "CREATED", reverse: true },
    { label: "OLDEST ARRIVALS", sortKey: "CREATED", reverse: false },
    { label: "PRICE: LOW TO HIGH", sortKey: "PRICE", reverse: false },
    { label: "PRICE: HIGH TO LOW", sortKey: "PRICE", reverse: true },
    { label: "TITLE A → Z", sortKey: "TITLE", reverse: false },
    { label: "TITLE Z → A", sortKey: "TITLE", reverse: true },
    { label: "MOST RELEVANT", sortKey: "RELEVANCE", reverse: false },
  ];

  const screenHeight = Dimensions.get("window").height;
  const [filters, setFilters] = useState([]);
  const [activeFilter, setActiveFilter] = useState("");
  const [activeIndex, setActiveIndex] = useState(0);
  const [filterSelections, setFilterSelections] = useState<any>({});
  const [cursor, setCursor] = useState<string | null>(null);
  const [hasNextPage, setHasNextPage] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [sortKey, setSortKey] = useState("BEST_SELLING");
  const [reverse, setReverse] = useState(false);
  const [selectedSort, setSelectedSort] = useState({
    sortKey: "BEST_SELLING",
    reverse: false,
  });
  const [loading, setLoading] = useState(true);
  const ITEMS_LIMIT = 10;
  const scrollX = useRef(new Animated.Value(0)).current;
  const [priceRange, setPriceRange] = useState({ min: 0, max: 100 });
  const scrollRef = useRef(null);
  const { width } = Dimensions.get("window");
  const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } =
    Dimensions.get("window");
  const CARD_WIDTH = (SCREEN_WIDTH - 48) / 2; // 16px padding on each side + 16px gap between cards
  const BANNER_HEIGHT = SCREEN_HEIGHT * 0.2;
  const [refetching, setRefetching] = useState(false);
  const {
    loading: queryLoading,
    error,
    data,
    refetch,
    fetchMore,
  } = useQuery(GET_COLLECTION_PRODUCTS, {
    variables: {
      handle,
      first: ITEMS_LIMIT,
      after: null,
      filters: [],
      sortKey,
      reverse,
    },
    onCompleted: () => {
      setLoading(false); // Set loading to false when query is completed
    },
  });
  const didMountRef = useRef(false);

  useEffect(() => {
    const fetchData = async () => {
      if (!handle || !didMountRef.current) {
        didMountRef.current = true;
        return; // Skip first render — useQuery already loads initially
      }

      setLoading(true);
      setRefetching(true);

      try {
        await refetch({
          handle,
          first: ITEMS_LIMIT,
          after: null,
          filters: [],
          sortKey,
          reverse,
        });
      } catch (error) {
        console.error("Refetch error", error);
      } finally {
        setRefetching(false);
        setLoading(false);
      }
    };

    fetchData();
  }, [handle]);

  useEffect(() => {
    if (!data?.collectionByHandle?.products?.nodes) return;

    const newProducts = data.collectionByHandle.products.nodes;

    const formatted = newProducts.map((node: any) => {
      const variants = node?.variants?.edges?.map((variantEdge: any) => ({
        id: variantEdge?.node?.id,
        title: variantEdge?.node?.title,
        price: variantEdge?.node?.price.amount || "0.0",
        currency: variantEdge?.node?.price.currencyCode,
        image: variantEdge?.node?.image ? variantEdge?.node?.image.url : null,
      }));
      const sizeOption = node?.options?.find(
        (option: any) => option?.name?.toLowerCase() === "size"
      );
      return {
        id: node?.id,
        title: node?.title,
        handle: node?.handle,
        description: node?.description,
        image:
          node?.images?.edges?.length > 0
            ? node?.images?.edges[0]?.node?.src
            : null,
        price: node?.variants?.edges[0]?.node?.price?.amount || "0.0",
        currency: node?.variants.edges[0]?.node?.price?.currencyCode || "SGD",
        variants,
        sizes: sizeOption?.values || [],
      };
    });

    setProductList((prev: any) => {
      const existingIds = new Set(prev.map((p: any) => p.id));
      const uniqueNewProducts = formatted.filter(
        (p: any) => !existingIds.has(p.id)
      );
      return [...prev, ...uniqueNewProducts];
    });

    const pageInfo = data.collectionByHandle.products.pageInfo;
    setCursor(pageInfo?.endCursor || null);
    setHasNextPage(pageInfo?.hasNextPage || false);
  }, [data]);

  const formattedFilters = useMemo(() => {
    if (!data?.collectionByHandle?.products?.filters) return [];

    return data?.collectionByHandle?.products?.filters?.map((filter: any) => ({
      filter: filter.label,
      id: filter.id,
      type: filter.type,
      options: filter.values.map((val: any) => ({
        label: val.label,
        count: val.count || 0,
        color: val.swatch?.color || null,
        input: val?.input || null, // ✅ ADD THIS LINE
      })),
    }));
  }, [data]);

  useEffect(() => {
    if (formattedFilters.length) {
      setFilters(formattedFilters);
      setActiveFilter(formattedFilters[0]?.filter); // Optional: set default active filter
    }
  }, [formattedFilters]);

  const openBottomSheet = useCallback((filter: any) => {
    setModalVisible(true);
  }, []);
  const handleOpenFilter = () => {
    setFilterSortOptions(true);
    openBottomSheet(true);
  };

  const handleOpenSort = () => {
    setFilterSortOptions(false);
    openBottomSheet(false);
  };

  const images = [
    "https://m.media-amazon.com/images/G/31/img21/shoes/February/PMP/Footwear._CB660415381_.jpg",
    "https://m.media-amazon.com/images/G/31/img21/shoes/February/PMP/Footwear._CB660415381_.jpg",
    "https://m.media-amazon.com/images/G/31/img21/shoes/February/PMP/Footwear._CB660415381_.jpg",
  ];

  const toggleOption = (filterLabel: any, option: any) => {
    setFilterSelections((prev: any) => {
      const current = prev[filterLabel] || [];
      const exists = current.find((o: any) => o.label === option.label);
      const updated = {
        ...prev,
        [filterLabel]: exists
          ? current.filter((o: any) => o.label !== option.label)
          : [...current, option],
      };
      return updated;
    });
  };

  const buildShopifyFilters = () => {
    const appliedFilters: any[] = [];
    if (priceRange.min !== 0 || priceRange.max !== 100) {
      appliedFilters.push({
        price: { min: priceRange.min, max: priceRange.max },
      });
    }
    Object.entries(filterSelections).forEach(
      ([filterLabel, selectedOptions]: any) => {
        const labelKey = filterLabel.toLowerCase().replace(/ /g, "_");

        if (filterLabel === "Availability") {
          selectedOptions.forEach((val: any) => {
            const label = typeof val === "string" ? val : val.label;
            const lowerValue = label?.toLowerCase?.();

            if (lowerValue === "in stock") {
              appliedFilters.push({ available: true });
            } else if (lowerValue === "out of stock") {
              appliedFilters.push({ available: false });
            }
          });
        } else if (labelKey === "color") {
          selectedOptions.forEach((option: any) => {
            if (option.input) {
              try {
                const parsedInput = JSON.parse(option.input);
                // console.log("option.input", option.input);
                appliedFilters.push(parsedInput);
              } catch (e) {
                console.error(
                  "Invalid input JSON for color filter:",
                  option.input
                );
              }
            } else {
              console.warn("Missing `input` for color option:", option);
            }
          });
        } else if (labelKey === "brand") {
          selectedOptions.forEach((option: any) => {
            if (option.input) {
              try {
                const parsedInput = JSON.parse(option.input);
                appliedFilters.push(parsedInput);
              } catch (e) {
                console.error(
                  "Invalid input JSON for brand filter:",
                  option.input
                );
              }
            } else {
              console.warn("Missing `input` for brand option:", option);
            }
          });
        } else {
          selectedOptions.forEach((val: any) => {
            appliedFilters.push({
              [labelKey]: typeof val === "string" ? val : val.label,
            });
          });
        }
      }
    );
    console.log("appliedFilters >>>", appliedFilters);

    return appliedFilters;
  };
  const applyFilter = async () => {
    setModalVisible(false);

    const appliedFilters = buildShopifyFilters();

    setCursor(null);
    setProductList([]); // Clear old products

    setRefetching(true);
    const result = await refetch({
      handle,
      filters: appliedFilters,
      sortKey,
      reverse,
      after: null,
      first: ITEMS_LIMIT,
    });
    setRefetching(false);
    const pageInfo = result?.data?.collectionByHandle?.products?.pageInfo;
    setCursor(pageInfo?.endCursor || null);
    setHasNextPage(pageInfo?.hasNextPage || false);
  };
  const applySorting = async (newSort: {
    sortKey: string;
    reverse: boolean;
  }) => {
    setSelectedSort(newSort);

    const appliedFilters = buildShopifyFilters();

    setCursor(null);
    setProductList([]);
    setLoading(true);
    setRefetching(true);

    const result = await refetch({
      handle,
      filters: appliedFilters,
      sortKey: newSort.sortKey,
      reverse: newSort.reverse,
      after: null,
      first: ITEMS_LIMIT,
    });

    const pageInfo = result?.data?.collectionByHandle?.products?.pageInfo;
    setCursor(pageInfo?.endCursor || null);
    setHasNextPage(pageInfo?.hasNextPage || false);
    setLoading(false);
    setRefetching(false);
  };

  const clearAllFilters = () => {
    setFilterSelections({});
    setPriceRange({ min: 0, max: 100 });
    setRefetching(true);
    refetch({
      handle,
      filters: [],
      sortKey: "BEST_SELLING",
      reverse: false,
    });
    setModalVisible(false);
    setRefetching(false);
  };
  const loadMoreProducts = async () => {
    if (!hasNextPage || loadingMore) return;

    setLoadingMore(true);

    const appliedFilters = buildShopifyFilters();

    try {
      const moreData = await fetchMore({
        variables: {
          handle,
          first: ITEMS_LIMIT,
          after: cursor,
          filters: appliedFilters,
          sortKey: selectedSort.sortKey, // ✅ use current sort
          reverse: selectedSort.reverse,
        },
      });

      const newProducts =
        moreData?.data?.collectionByHandle?.products?.nodes || [];
      const newCursor =
        moreData?.data?.collectionByHandle?.products?.pageInfo?.endCursor;
      const next =
        moreData?.data?.collectionByHandle?.products?.pageInfo?.hasNextPage;

      const formatted = newProducts.map((node: any) => {
        const variants = node?.variants?.edges?.map((variantEdge: any) => ({
          id: variantEdge?.node?.id,
          title: variantEdge?.node?.title,
          price: variantEdge?.node?.price.amount || "0.0",
          currency: variantEdge?.node?.price.currencyCode,
          image: variantEdge?.node?.image ? variantEdge?.node?.image.url : null,
        }));

        return {
          id: node?.id,
          title: node?.title,
          handle: node?.handle,
          description: node?.description,
          image:
            node?.images?.edges?.length > 0
              ? node?.images?.edges[0]?.node?.src
              : null,
          price: node?.variants?.edges[0]?.node?.price?.amount || "0.0",
          currency: node?.variants.edges[0]?.node?.price?.currencyCode || "SGD",
          variants,
        };
      });

      setProductList((prev: any) => {
        const existingIds = new Set(prev.map((p: any) => p.id));
        const uniqueNewProducts = formatted.filter(
          (p: any) => !existingIds.has(p.id)
        );
        return [...prev, ...uniqueNewProducts];
      });

      setCursor(newCursor);
      setHasNextPage(next);
    } catch (err) {
      console.error("Error loading more products", err);
    }

    setLoadingMore(false);
  };
  // if (queryLoading || loading || refetching) {
  //   return (
  //     <View
  //       style={{
  //         justifyContent: "center",
  //         alignItems: "center",
  //         height: 750,
  //       }}
  //     >
  //       <ActivityIndicator size="large" color={COLORS.primary} />
  //     </View>
  //   );
  // }

  return (
    <SafeAreaView style={{ backgroundColor: COLORS.background, flex: 1 }}>
      <Header
        title={headerBarTitle ?? handle}
        // rightIcon={"cart"}
        leftIcon={"back"}
        titleLeft
        paddingLeft
        totalItems={!searchQuery && productList?.length}
      />

      {queryLoading || loading || refetching ? (
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            height: 750,
          }}
        >
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      ) : productList?.length > 0 ? (
        <>
          <FlatList
            data={productList}
            keyExtractor={(item: any, index: any) =>
              item.id || index.toString()
            }
            renderItem={({ item }) => (
              <SingleProductCard
                badge="15% OFF"
                data={item}
                navigation={navigation}
                handle={item?.handle}
              />
            )}
            numColumns={2}
            columnWrapperStyle={{
              justifyContent: "space-around",
              marginHorizontal: 17,
              marginTop: SCREEN_HEIGHT * 0.02, // 2% of screen height
              gap: 5,
            }}
            contentContainerStyle={{
              paddingBottom: SCREEN_HEIGHT * 0.12, // 12% of screen height
            }}
            ListHeaderComponent={
              !searchQuery ? (
                <View
                  style={{
                    alignItems: "center",
                    marginTop: SCREEN_HEIGHT * 0.02,
                    marginHorizontal: 15,
                  }}
                >
                  <ScrollView
                    horizontal
                    pagingEnabled
                    ref={scrollRef}
                    showsHorizontalScrollIndicator={false}
                    scrollEventThrottle={16}
                    contentContainerStyle={{
                      width: SCREEN_WIDTH * images.length,
                    }}
                    onScroll={Animated.event(
                      [{ nativeEvent: { contentOffset: { x: scrollX } } }],
                      {
                        useNativeDriver: false,
                        listener: (event: any) => {
                          const xOffset = event.nativeEvent.contentOffset.x;
                          const newIndex = Math.round(xOffset / SCREEN_WIDTH);
                          setActiveIndex(newIndex);
                        },
                      }
                    )}
                  >
                    {images?.map((img, index) => {
                      return (
                        <View style={{}}>
                          <Image
                            key={index}
                            source={{ uri: img }}
                            style={{
                              width: SCREEN_WIDTH - 32,
                              height: BANNER_HEIGHT,
                              objectFit: "cover",
                              borderRadius: 10,
                              marginHorizontal: 1,
                            }}
                          />
                        </View>
                      );
                    })}
                  </ScrollView>

                  <View
                    style={{
                      flexDirection: "row",
                      marginTop: SCREEN_HEIGHT * 0.01,
                      position: "absolute",
                      top: BANNER_HEIGHT - 20,
                    }}
                  >
                    {images?.map((_, index) => (
                      <View
                        key={index}
                        style={{
                          width: activeIndex === index ? 20 : 6,
                          height: 6,
                          borderRadius: 5,
                          marginHorizontal: 4,
                          backgroundColor:
                            activeIndex === index ? COLORS.black : COLORS.white,
                        }}
                      />
                    ))}
                  </View>
                </View>
              ) : (
                <View
                  style={{
                    marginVertical: SCREEN_HEIGHT * 0.02,
                    marginHorizontal: 16,
                    flexDirection: "row",
                    flexWrap: "wrap",
                  }}
                >
                  <Text
                    style={{ ...FONTS.fontLg }}
                  >{`${productList?.length} Results found for "`}</Text>
                  <Text
                    style={{ fontWeight: "700", ...FONTS.fontLg }}
                  >{`${searchQuery}".`}</Text>
                </View>
              )
            }
            onEndReached={() => {
              if (!loadingMore && hasNextPage) {
                loadMoreProducts();
              }
            }}
            onEndReachedThreshold={0.1}
            ListFooterComponent={
              loadingMore ? (
                <View style={{ marginTop: SCREEN_HEIGHT * 0.04 }}>
                  <ActivityIndicator size="large" color={COLORS.primary} />
                </View>
              ) : hasNextPage ? (
                <Text
                  style={{ textAlign: "center", padding: SCREEN_HEIGHT * 0.02 }}
                ></Text>
              ) : null
            }
          />

          <View
            style={{
              position: "absolute",
              bottom: 0,
              left: 0,
              right: 0,
              height: 70,
              flexDirection: "row",
              justifyContent: "space-around",
              alignItems: "center",
              backgroundColor: COLORS.white,
              paddingVertical: 10,
              borderTopColor: COLORS.gray,
              zIndex: 10,
            }}
          >
            {/* Filter Button */}
            <TouchableOpacity
              style={{ flexDirection: "row", alignItems: "center", gap: 5 }}
              onPress={handleOpenFilter}
            >
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  gap: 13.5,
                }}
              >
                <Image
                  source={IMAGES.filter}
                  style={{ width: 22, height: 22 }}
                />
                <Text
                  style={{
                    ...FONTS.fontMedium,
                    fontSize: SIZES.fontLg,
                    color: "black",
                  }}
                >
                  Filter
                </Text>
              </View>

              {buildShopifyFilters()?.length > 0 && (
                <View
                  style={{
                    width: 25,
                    backgroundColor: "black",
                    height: 25,
                    borderRadius: 15,
                    padding: 5,
                    justifyContent: "center",
                  }}
                >
                  <Text
                    style={{
                      color: "white",
                      fontSize: 10,
                      textAlign: "center",
                    }}
                  >
                    {buildShopifyFilters()?.length}
                  </Text>
                </View>
              )}
            </TouchableOpacity>

            {/* Sort Button */}
            <TouchableOpacity
              style={{ flexDirection: "row", alignItems: "center", gap: 13.5 }}
              onPress={handleOpenSort}
            >
              <Image source={IMAGES.sort} style={{ width: 22, height: 22 }} />
              <Text
                style={{
                  ...FONTS.fontMedium,
                  fontSize: SIZES.fontLg,
                }}
              >
                Sort By
              </Text>
            </TouchableOpacity>
          </View>

          {filterSortOptions && (
            <BottomNavModal
              modalVisible={modalVisible}
              setModalVisible={setModalVisible}
              buttonTitle={ButtonLabel.clearAll}
              buttonWidth={200}
              height={screenHeight}
              leftbuttonTitle={ButtonLabel.clearAll}
              rightButtonTitle={ButtonLabel.apply}
              clearAllBtn={true}
              navbarTitle="Filter Results"
              planBottomSheet={true}
              onPressRightBtn={applyFilter}
              onPressLeftBtn={clearAllFilters}
            >
              <View style={styles.container}>
                <View style={styles.section40}>
                  {filters?.map((filter, index) => {
                    return (
                      <TouchableOpacity
                        key={filter?.id}
                        activeOpacity={1}
                        onPress={() => {
                          setActiveFilter(filter?.filter);
                        }}
                        style={{
                          backgroundColor:
                            activeFilter === filter?.filter
                              ? "#E9E9E9"
                              : COLORS.white,
                          width: "100%",
                          height: 50,
                          justifyContent: "center",
                          borderWidth: 0.5,
                          borderColor: "#00000033",
                          borderTopColor: activeFilter && "red",
                        }}
                      >
                        <View
                          style={{
                            width: "100%",
                            justifyContent: "flex-start",
                            alignItems: "center",
                            flexDirection: "row",
                            marginHorizontal: 20,
                            gap: 10,
                          }}
                        >
                          <Text
                            style={{
                              ...FONTS.fontMedium,
                              fontSize: SIZES.fontLg,
                            }}
                          >
                            {filter.filter}
                          </Text>
                          {filterSelections[filter.filter]?.length > 0 && (
                            <View
                              style={{
                                width: 25,
                                backgroundColor: "black",
                                height: 25,
                                borderRadius: 15,
                                padding: 5,
                                justifyContent: "center",
                              }}
                            >
                              <Text
                                style={{
                                  color: "white",
                                  fontSize: 10,
                                  textAlign: "center",
                                }}
                              >
                                {filterSelections[filter.filter]?.length}
                              </Text>
                            </View>
                          )}
                        </View>
                      </TouchableOpacity>
                    );
                  })}
                </View>

                <View style={styles.section60}>
                  {filters?.map((filter: any, index: any) => {
                    return (
                      filter?.filter === activeFilter &&
                      filter?.options?.map((option: any) => {
                        const optionLabel = option.label;
                        const isChecked = !!filterSelections[
                          filter.filter
                        ]?.some((o) => o.label === optionLabel);

                        return (
                          <View
                            key={`${filter.filter}-${optionLabel}`}
                            style={{
                              flexDirection: "row",
                              width: 150,
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            {activeFilter === "Price" && (
                              <>
                                <View
                                  style={{
                                    justifyContent: "center",
                                    marginHorizontal: 40,
                                  }}
                                >
                                  <PriceRangeSlider
                                    min={0}
                                    max={100}
                                    onChange={(range: any) => {
                                      setPriceRange(range);
                                    }}
                                  />
                                  <Text>
                                    ${priceRange.min} - ${priceRange.max}
                                  </Text>
                                </View>
                              </>
                            )}
                            {activeFilter !== "Price" && (
                              <>
                                <CheckBox
                                  key={optionLabel}
                                  label={optionLabel}
                                  checkedState={{
                                    show: isChecked,
                                    setshow: () =>
                                      toggleOption(filter.filter, option),
                                    checkedUnChecked: () =>
                                      toggleOption(filter.filter, option),
                                  }}
                                />

                                <Text
                                  style={{
                                    ...FONTS.fontRegular,
                                    marginHorizontal: 12,
                                  }}
                                >
                                  {option.count}
                                </Text>
                              </>
                            )}
                          </View>
                        );
                      })
                    );
                  })}
                </View>
              </View>
            </BottomNavModal>
          )}
          {!filterSortOptions && (
            <BottomNavModal
              modalVisible={modalVisible}
              setModalVisible={setModalVisible}
              leftbuttonTitle={false}
              rightButtonTitle={false}
              clearAllBtn={false}
              buttonWidth={350}
              noButtton={true}
              headerEnabled={true}
              height={400}
              planBottomSheet={true}
              isCloseButtonRequired={false}
              isBackBtnRequired={false}
            >
              <View style={{ gap: 10 }}>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 8,
                    marginTop: 20,
                    marginHorizontal: 20,
                  }}
                >
                  <Image
                    source={IMAGES.sort}
                    style={{ width: 24, height: 24 }}
                  />
                  <Text
                    style={{
                      color: COLORS.black,
                      ...FONTWEIGHT.Black,
                      ...FONTS.font,
                    }}
                  >
                    Sort By
                  </Text>
                </View>
                <View>
                  <View style={{ marginHorizontal: 5 }}>
                    <RadioButton.Group
                      onValueChange={async (newValue) => {
                        const foundSort = sortOptions.find(
                          (item) => item.label === newValue
                        );
                        if (!foundSort) return;

                        const isSameSortSelected = selectedValue === newValue;
                        const updatedSort = isSameSortSelected
                          ? { sortKey: "BEST_SELLING", reverse: false }
                          : {
                              sortKey: foundSort.sortKey,
                              reverse: foundSort.reverse,
                            };

                        setSelectedValue(isSameSortSelected ? "" : newValue);
                        setModalVisible(false);

                        await applySorting(updatedSort);
                      }}
                      value={selectedValue}
                    >
                      {sortOptions.map((option) => (
                        <RadioButton.Item
                          key={option.label}
                          label={option.label}
                          value={option.label}
                          color="black"
                          labelStyle={{
                            ...FONTS.font,
                            fontWeight:
                              selectedValue === option.label ? "700" : "500",
                            color: "#4F4F4F",
                          }}
                        />
                      ))}
                    </RadioButton.Group>

                    {/* <Text style={{ marginTop: 10 }}>Selected: {selectedValue}</Text> */}
                  </View>
                </View>
              </View>
            </BottomNavModal>
          )}
        </>
      ) : (
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            height: 600,
          }}
        >
          <Text style={{ ...FONTS.font }}>No results found</Text>
        </View>
      )}
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: "row",
  },
  section40: {
    flex: 4,
    // borderWidth:0.3,

    backgroundColor: COLORS.white,
    height: 750,
    borderWidth: 0.5,
    borderTopColor: "white",
    // borderColor: COLORS.darkgray,
    borderBottomColor: COLORS.white,
    borderRightColor: "#00000033",
  },
  section60: {
    flex: 6,
    backgroundColor: COLORS.white,
  },
  text: {
    fontSize: 18,
    fontWeight: "bold",
  },
});
