import { useTheme } from '@react-navigation/native';
import React from 'react';
import { View, Text, SafeAreaView, Image, TouchableOpacity } from 'react-native';
import { COLORS, FONTS } from '../../constants/theme';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import Customotp from '../../components/Input/Customotp';
import Button from '../../components/Button/Button';
import {Feather} from "@expo/vector-icons";
import { IMAGES } from '../../constants/Images';
import { StackScreenProps } from '@react-navigation/stack';
import { RootStackParamList } from '../../Navigations/RootStackParamList';
import "react-native-gesture-handler";

type EnterCodeScreenProps = StackScreenProps<RootStackParamList, 'EnterCode'>;

const EnterCode = ({ navigation } : EnterCodeScreenProps) => {

    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    return (
        <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
            <View style={[GlobalStyleSheet.container,{padding:0}]}>
                <View>
                    <Image
                        style={{ height:null, aspectRatio: 2.3 / 1, width: '100%', borderBottomRightRadius: 100 }}
                        source={IMAGES.item4}
                    />
                </View>
                <TouchableOpacity
                    style={{
                        position: 'absolute',
                        top: 15,
                        left: 15
                    }}
                    onPress={() => navigation.goBack()}
                >
                    <View style={GlobalStyleSheet.background}>
                        <Image
                            style={{ height: 18, width: 18, resizeMode: 'contain', tintColor: COLORS.white }}
                            source={IMAGES.arrowleft}
                        />
                    </View>
                </TouchableOpacity>
            </View>
            <View style={[GlobalStyleSheet.container, { paddingHorizontal: 30, paddingTop: 30, flex: 1 }]}>
                <View style={{ flex: 1 }}>
                    <View style={{ alignItems: 'center' }}>
                        <Text style={{ ...FONTS.fontMedium, fontSize: 24, color: colors.title, marginBottom: 5 }}>Enter Code</Text>
                        <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>An Authentication Code Has Sent To </Text>
                        <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title, borderBottomWidth: 1, borderBottomColor: colors.title }}><EMAIL> </Text>
                    </View>
                    <View style={[{ alignItems: 'center', marginTop: 20 }]}>
                        <View style={{alignItems:'center'}}>
                            <Customotp />
                        </View>
                        <View style={{ flexDirection: 'row', marginTop: 10 }}>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>If you don't receive code! </Text>
                            <TouchableOpacity
                                onPress={() => navigation.navigate('ForgatPassword')}
                            >
                                <Text style={{ ...FONTS.fontMedium, borderBottomWidth: 1, borderBottomColor: colors.title, color: colors.title }}>Resend</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
                <View>
                    <Button
                        title={'Verify and proceed'}
                        btnRounded
                        fullWidth
                        icon={<Feather  size={24} color={colors.title} name={'arrow-right'} />}
                        onPress={() => navigation.navigate('NewPassword')}
                        color={colors.title}
                    />
                    <View style={{ alignItems: 'center', marginTop: 10, flexDirection: 'row', justifyContent: 'center' }}>
                        <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>Back To </Text>
                        <TouchableOpacity
                            onPress={() => navigation.navigate('SignIn')}
                        >
                            <Text style={{
                                ...FONTS.fontMedium,
                                borderBottomWidth: 1,
                                borderBottomColor: colors.title,
                                color: colors.title
                            }}>
                                Sign In
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </SafeAreaView>
    )
}

export default EnterCode;