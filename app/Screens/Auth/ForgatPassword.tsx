import React from 'react';
import { useTheme } from '@react-navigation/native';
import { View, Text, SafeAreaView, Image, TouchableOpacity } from 'react-native';
import { FONTS, COLORS } from '../../constants/theme';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import CustomInput from '../../components/Input/CustomInput';
import Button from '../../components/Button/Button';
import {Feather} from "@expo/vector-icons";
import { ScrollView } from 'react-native-gesture-handler';
import { IMAGES } from '../../constants/Images';
import { StackScreenProps } from '@react-navigation/stack';
import { RootStackParamList } from '../../Navigations/RootStackParamList';
import "react-native-gesture-handler";

type ForgatPasswordScreenProps = StackScreenProps<RootStackParamList, 'ForgatPassword'>;

const ForgatPassword = ({ navigation } : ForgatPasswordScreenProps) => {

    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    return (
        <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
            <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
                <View style={[GlobalStyleSheet.container,{padding:0}]}>
                    <View>
                        <Image
                            style={{ height: null, aspectRatio: 2.3 / 1, width: '100%', borderBottomRightRadius: 100 }}
                            source={IMAGES.item5}
                        />
                    </View>
                    <TouchableOpacity
                        style={{
                            position: 'absolute',
                            top: 15,
                            left: 15
                        }}
                        onPress={() => navigation.goBack()}
                    >
                        <View style={GlobalStyleSheet.background}>
                            <Image
                                style={{ height: 18, width: 18, resizeMode: 'contain', tintColor: COLORS.white }}
                                source={IMAGES.arrowleft}
                            />
                        </View>
                    </TouchableOpacity>
                </View>
                <View style={[GlobalStyleSheet.container, { paddingHorizontal: 30, paddingTop: 30, flex: 1 }]}>
                    <View style={{ flex: 1 }}>
                        <View>
                            <Text style={{ ...FONTS.fontMedium, fontSize: 24, color: colors.title, marginBottom: 5 }}>Forgot Password</Text>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>Enter the email associated with your account and
                                we'll send and email to reset your password</Text>
                        </View>
                        <View style={{ marginBottom: 15, marginTop: 30 }}>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>Email Address<Text style={{ color: '#FF0000' }}>*</Text></Text>
                            <CustomInput
                                 onChangeText={(value: any) => console.log(value)}
                            />
                        </View>
                    </View>
                    <View style={{}}>
                        <Button
                            title={'Send Mail'}
                            btnRounded
                            fullWidth
                            icon={<Feather  size={24} color={colors.title} name={'arrow-right'} />}
                            onPress={() => navigation.navigate('EnterCode')}
                            color={colors.title}
                        />
                        <View style={{ alignItems: 'center', marginTop: 10, flexDirection: 'row', justifyContent: 'center' }}>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>Back To </Text>
                            <TouchableOpacity
                                onPress={() => navigation.navigate('SignIn')}
                            >
                                <Text style={{
                                    ...FONTS.fontMedium,
                                    borderBottomWidth: 1,
                                    borderBottomColor: colors.title,
                                    color: colors.title
                                }}>
                                    Sign In
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </SafeAreaView>
    )
}

export default ForgatPassword;