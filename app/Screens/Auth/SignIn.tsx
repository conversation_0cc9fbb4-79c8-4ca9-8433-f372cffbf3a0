import { useTheme } from "@react-navigation/native";
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Linking,
} from "react-native";
import { FONTS, COLORS } from "../../constants/theme";
import { GlobalStyleSheet } from "../../constants/StyleSheet";
import CustomInput from "../../components/Input/CustomInput";
import Button from "../../components/Button/Button";
import { Feather } from "@expo/vector-icons";
import SocialBtn from "../../components/Socials/SocialBtn";
import { FontAwesome } from "@expo/vector-icons";
import { ScrollView } from "react-native-gesture-handler";
import { IMAGES } from "../../constants/Images";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList } from "../../Navigations/RootStackParamList";
import { gql, useMutation } from "@apollo/client";
import client from "../../api/appoloClient";
import { authorize } from "react-native-app-auth";
import { WebView, WebViewNavigation } from "react-native-webview";
import {
  useAuthRequest,
  makeRedirectUri,
  exchangeCodeAsync,
} from "expo-auth-session";
import * as Crypto from "expo-crypto";
import * as SecureStore from "expo-secure-store";
import { useShopifyAuth } from "../../api/shopifyAuth";
import {
  generateCodeVerifier,
  generateCodeChallenge,
} from "../../helpers/authHelpers";
import "react-native-gesture-handler";
type SignInScreenProps = StackScreenProps<RootStackParamList, "SignIn">;

const SHOPIFY_STORE_URL = "https://sunrise-trade.myshopify.com";
const SHOPIFY_CLIENT_ID = "shp_14fe4f79-cc74-42bb-8f31-e072f2ff10d4";
const SHOPIFY_SHOP = "sunrise-trade";
const CALLBACK_URL = "https://shop.65050247268.app/callback";

const SHOPIFY_SHOP_ID = "65050247268";
const REDIRECT_URI = makeRedirectUri({ scheme: "shop.65050247268.app" });
const AUTH_URL = `https://shopify.com/authentication/${SHOPIFY_SHOP_ID}/oauth/authorize`;

const discovery = {
  authorizationEndpoint: `https://${SHOPIFY_SHOP}.myshopify.com/authentication/oauth/authorize`,
  tokenEndpoint: `https://${SHOPIFY_SHOP}.myshopify.com/authentication/oauth/token`,
};

const shopifyConfig = {
  issuer: SHOPIFY_STORE_URL,
  clientId: SHOPIFY_CLIENT_ID,
  redirectUrl: "shop.65050247268.app/callback",
  scopes: ["openid", "profile", "email"],
  serviceConfiguration: {
    authorizationEndpoint: "https://shopify.com/authentication/65050247268/oauth/authorize",
    tokenEndpoint: "https://shopify.com/authentication/65050247268/oauth/token"
  }
};

const SignIn = ({ navigation }: SignInScreenProps) => {
  const theme = useTheme();
  const { colors }: { colors: any } = theme;
  const [otp, setOtp] = useState("");

  const [otpSent, setOtpSent] = useState(false);
  const [email, setEmail] = useState("");
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [authRequest, setAuthRequest]: any = useState(null);
  const [authCode, setAuthCode] = useState<string | null>(null);

  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);

  const { requestOtp, verifyOtp, loading } = useShopifyAuth();
  const handleRequestOtp = async () => {
    if (!email) {
      Alert.alert("Error", "Please enter your email.");
      return;
    }
    await requestOtp(email);
    setOtpSent(true);
  };

  const handleVerifyOtp = async () => {
    if (!otp) {
      Alert.alert("Error", "Please enter the OTP.");
      return;
    }
    const token = await verifyOtp(email, otp);
    if (token) {
      // Save token and navigate to home
      navigation.navigate("DrawerNavigation", { screen: "Home" });
    }
  };

  const [request, response, promptAsync] = useAuthRequest(
    {
      clientId: SHOPIFY_CLIENT_ID,
      redirectUri: REDIRECT_URI,
      scopes: ["openid", "email", "customer-account-api:full"],
      responseType: "code",
    },
    discovery
  );

  useEffect(() => {
    console.log("---resp", response);
    if (response?.type === "success") {
      console.log("--response", response);
      const { code } = response.params;
      exchangeForToken(code);
    }
  }, [response]);

  async function exchangeForToken(code: string) {
    console.log("--getting token");
    try {
      const tokenResponse = await exchangeCodeAsync(
        {
          clientId: SHOPIFY_CLIENT_ID,
          code,
          redirectUri: REDIRECT_URI,
          extraParams: {
            code_verifier: request?.codeVerifier || "",
          },
        },
        discovery
      );
      console.log("---tokenRE", tokenResponse);
      setAccessToken(tokenResponse.accessToken);
      console.log("Access Token:", tokenResponse.accessToken);

      fetchUserInfo(tokenResponse.accessToken);
    } catch (error) {
      console.error("Token Exchange Failed:", error);
    }
  }

  // 🔹 Fetch user information (optional)
  async function fetchUserInfo(token: string) {
    try {
      const response = await fetch(
        `https://${SHOPIFY_SHOP}.myshopify.com/customer/account`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();
      setUserEmail(data?.email || "Unknown");
      console.log("User Info:", data);
    } catch (error) {
      console.error("Failed to Fetch User Info:", error);
    }
  }
  return (
    <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
      <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
        <View style={[GlobalStyleSheet.container, { padding: 0 }]}>
          <View>
            <Image
              style={{
                height: null,
                aspectRatio: 2.3 / 1,
                width: "100%",
                borderBottomRightRadius: 100,
              }}
              source={IMAGES.item4}
            />
          </View>
        </View>
        <View
          style={[
            GlobalStyleSheet.container,
            { paddingHorizontal: 30, paddingTop: 30 },
          ]}
        >
          <View>
            <Text
              style={{
                ...FONTS.fontMedium,
                fontSize: 24,
                color: colors.title,
                marginBottom: 5,
              }}
            >
              Sign in to your account
            </Text>
            <Text
              style={{
                ...FONTS.fontRegular,
                fontSize: 15,
                color: colors.title,
              }}
            >
              Welcome Back You've Been Missed!
            </Text>
          </View>
          <View style={{ marginBottom: 15, marginTop: 30 }}>
            <Text
              style={{
                ...FONTS.fontRegular,
                fontSize: 15,
                color: colors.title,
              }}
            >
              Email Address<Text style={{ color: "#FF0000" }}>*</Text>
            </Text>
            <CustomInput
              onChangeText={setEmail}
              // onChangeText={(value: any) => console.log(value)}
            />
          </View>
          <View style={{ marginTop: 20 }}>
            {/* <Button title="Request OTP" onPress={handleRequestOtp} /> */}
            <Button title="Login with Shopify" onPress={() => promptAsync()} />
          </View>

          {/* <Button
            title="Sign in with Shopify"
            btnRounded
            fullWidth
            onPress={() => promptAsync()} // Call Shopify login
            // disabled={!request} // Disable if request isn't ready
            color={colors.title}
          /> */}
          <View style={{ marginTop: 72 }}>
            <Button
              title={"Sign in"}
              btnRounded
              fullWidth
              icon={
                <Feather size={24} color={colors.title} name={"arrow-right"} />
              }
              // onPress={handleSendOtp}

              onPress={() => navigation.navigate("CompanyRegistration")}
              color={colors.title}
            />
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginTop: 37,
                marginBottom: 30,
              }}
            >
              <View
                style={{
                  height: 1,
                  flex: 1,
                  backgroundColor: colors.title,
                }}
              />
              <Text
                style={{
                  ...FONTS.fontMedium,
                  color: colors.text,
                  marginHorizontal: 15,
                  fontSize: 13,
                }}
              >
                Or continue with
              </Text>
              <View
                style={{
                  height: 1,
                  flex: 1,
                  backgroundColor: colors.title,
                }}
              />
            </View>
            <View style={{ marginBottom: 10 }}>
              <SocialBtn
                icon={
                  <Image
                    style={{ height: 20, width: 20, resizeMode: "contain" }}
                    source={IMAGES.google2}
                  />
                }
                rounded
                color={"#E8E2DB"}
                text={"Sign in with google"}
              />
            </View>
            <View>
              <SocialBtn
                icon={
                  <FontAwesome name="apple" size={20} color={COLORS.title} />
                }
                rounded
                color={"#E8E2DB"}
                text={"Sign in with apple"}
              />
            </View>
            <View
              style={{
                alignItems: "center",
                marginTop: 20,
                flexDirection: "row",
                justifyContent: "center",
              }}
            >
              <Text
                style={{
                  ...FONTS.fontRegular,
                  fontSize: 15,
                  color: colors.title,
                }}
              >
                Not a member?
              </Text>
              <TouchableOpacity onPress={() => navigation.navigate("SignUp")}>
                <Text
                  style={{
                    ...FONTS.fontMedium,
                    borderBottomWidth: 1,
                    borderBottomColor: colors.title,
                    color: colors.title,
                  }}
                >
                  {" "}
                  Create an account
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </SafeAreaView>
    </ScrollView>
  );
};

export default SignIn;
