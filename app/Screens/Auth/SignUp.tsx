import React, { useState } from 'react';
import { useTheme } from '@react-navigation/native';
import { View, Text, Image, TouchableOpacity, SafeAreaView } from 'react-native';
import { FONTS, COLORS } from '../../constants/theme';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import CustomInput from '../../components/Input/CustomInput';
import Button from '../../components/Button/Button';
import {Feather} from "@expo/vector-icons";
import SocialBtn from '../../components/Socials/SocialBtn';
import {FontAwesome} from "@expo/vector-icons";
import { Checkbox } from 'react-native-paper';
import { ScrollView } from 'react-native-gesture-handler';
import { IMAGES } from '../../constants/Images';
import { StackScreenProps } from '@react-navigation/stack';
import { RootStackParamList } from '../../Navigations/RootStackParamList';
import "react-native-gesture-handler";

type SignUpScreenProps = StackScreenProps<RootStackParamList, 'SignUp'>;

const SignUp = ({ navigation } : SignUpScreenProps) => {

    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    const [isChecked, setisChecked] = useState(false);

    return (
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>

            <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
                <View style={[GlobalStyleSheet.container,{padding:0}]}>
                    <View>
                        <Image
                            style={{ height: null, aspectRatio: 2.3 / 1, width: '100%', borderBottomRightRadius: 100 }}
                            source={IMAGES.item7}
                        />
                    </View>
                    <TouchableOpacity
                        style={{
                            position: 'absolute',
                            top: 15,
                            left: 15
                        }}
                        onPress={() => navigation.goBack()}
                    >
                        <View style={GlobalStyleSheet.background}>
                            <Image
                                style={{ height: 18, width: 18, resizeMode: 'contain', tintColor: COLORS.white }}
                                source={IMAGES.arrowleft}
                            />
                        </View>
                    </TouchableOpacity>
                </View>
                <View style={[GlobalStyleSheet.container, { paddingHorizontal: 30, paddingTop: 30 }]}>
                    <View>
                        <Text style={{ ...FONTS.fontMedium, fontSize: 24, color: colors.title, marginBottom: 5 }}>Create your account</Text>
                        <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>Welcome back! Please enter your details</Text>
                    </View>
                    <View style={{ marginBottom: 15, marginTop: 30 }}>
                        <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>Name<Text style={{ color: '#FF0000' }}>*</Text></Text>
                        <CustomInput
                             onChangeText={(value: any) => console.log(value)}
                        />
                    </View>
                    <View style={{ marginBottom: 15 }}>
                        <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>Email Address<Text style={{ color: '#FF0000' }}>*</Text></Text>
                        <CustomInput
                             onChangeText={(value: any) => console.log(value)}
                        />
                    </View>
                    <View>
                        <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>Password<Text style={{ color: '#FF0000' }}>*</Text></Text>
                        <CustomInput
                            type={'password'}
                             onChangeText={(value: any) => console.log(value)}
                        />
                        <View>
                            <Checkbox.Item
                                onPress={() => setisChecked(!isChecked)}
                                position='leading'
                                label="I agree to all Term, Privacy and Fees"
                                color={colors.title}
                                uncheckedColor={colors.textLight}
                                status={isChecked ? "checked" : "unchecked"}
                                style={{
                                    paddingHorizontal: 0,
                                    paddingVertical: 5,
                                }}
                                labelStyle={{
                                    ...FONTS.fontRegular,
                                    fontSize: 15,
                                    color: colors.title,
                                    textAlign: 'left',
                                }}
                            />
                        </View>
                    </View>
                    <View style={{ marginTop: 32 }}>
                        <Button
                            title={'Sign Up'}
                            btnRounded
                            fullWidth
                            icon={<Feather  size={24} color={colors.title} name={'arrow-right'} />}
                            onPress={() => navigation.navigate('SignIn')}
                            color={colors.title}
                        />
                        <View
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                marginTop: 37,
                                marginBottom: 30,
                            }}
                        >
                            <View
                                style={{
                                    height: 1,
                                    flex: 1,
                                    backgroundColor: colors.title,
                                }}
                            />
                            <Text style={{
                                ...FONTS.fontMedium,
                                color: colors.text,
                                marginHorizontal: 15,
                                fontSize: 13
                            }}>Or continue with</Text>
                            <View
                                style={{
                                    height: 1,
                                    flex: 1,
                                    backgroundColor: colors.title,
                                }}
                            />
                        </View>
                        <View style={{ marginBottom: 10 }}>
                            <SocialBtn
                                icon={<Image style={{ height: 20, width: 20, resizeMode: 'contain' }} source={IMAGES.google2} />}
                                rounded
                                color={'#E8E2DB'}
                                text={'Sign in with google'}
                            />
                        </View>
                        <View>
                            <SocialBtn
                                icon={<FontAwesome name='apple' size={20} color={COLORS.title} />}
                                rounded
                                color={'#E8E2DB'}
                                text={'Sign in with apple'}
                            />
                        </View>
                        <View style={{ alignItems: 'center', marginTop: 10, flexDirection: 'row', justifyContent: 'center' }}>
                            <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>Already have and account? </Text>
                            <TouchableOpacity
                                onPress={() => navigation.navigate('SignIn')}
                            >
                                <Text style={{
                                    ...FONTS.fontMedium,
                                    borderBottomWidth: 1,
                                    borderBottomColor: colors.title,
                                    color: colors.title
                                }}>Sign In</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </SafeAreaView>
        </ScrollView>
    )
}

export default SignUp;