import React, { createContext, useContext, useState, ReactNode } from "react";
import { Snackbar } from "react-native-paper";
import { StyleSheet, View } from "react-native";
import { COLORS } from "../constants/theme";

interface SnackbarContextType {
  showSnackbar: (
    message: string,
    type?: "success" | "error" | "info" | "warning"
  ) => void;
  hideSnackbar: () => void;
}

const SnackbarContext = createContext<SnackbarContextType | undefined>(
  undefined
);

interface SnackbarProviderProps {
  children: ReactNode;
}

export const SnackbarProvider: React.FC<SnackbarProviderProps> = ({
  children,
}: any) => {
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState("");
  const [type, setType] = useState<"success" | "error" | "info" | "warning">(
    "info"
  );

  const showSnackbar = (
    message: string,
    type: "success" | "error" | "info" | "warning" = "info"
  ) => {
    setMessage(message);
    setType(type);
    setVisible(true);
  };

  const hideSnackbar = () => {
    setVisible(false);
  };

  const getSnackbarStyle = () => {
    switch (type) {
      case "success":
        return styles.success;
      case "error":
        return styles.error;
      case "warning":
        return styles.warning;
      case "info":
      default:
        return styles.info;
    }
  };

  return (
    <SnackbarContext.Provider value={{ showSnackbar, hideSnackbar }}>
      {children}
      <View style={styles.snackbarContainer}>
        <Snackbar
          visible={visible}
          onDismiss={hideSnackbar}
          duration={3000}
          style={[styles.snackbar, getSnackbarStyle()]}
        >
          {message}
        </Snackbar>
      </View>
    </SnackbarContext.Provider>
  );
};

export const useSnackbar = (): SnackbarContextType => {
  const context = useContext(SnackbarContext);
  if (!context) {
    throw new Error("useSnackbar must be used within a SnackbarProvider");
  }
  return context;
};

const styles = StyleSheet.create({
  snackbarContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
  },
  snackbar: {
    margin: 8,
    borderRadius: 4,
  },
  success: {
    backgroundColor: COLORS.primary,
  },
  error: {
    backgroundColor: "#D32F2F",
  },
  warning: {
    backgroundColor: "#FFA000",
  },
  info: {
    backgroundColor: "#1976D2",
  },
});
