import { gql, useLazyQuery } from "@apollo/client";

export const SEARCH_QUERY = gql`
  query search(
    $searchTerm: String!
    $country: CountryCode
    $language: LanguageCode
  ) @inContext(country: $country, language: $language) {
    search(query: $searchTerm, first: 10) {
      nodes {
        ... on Product {
          title
          handle
          tags
          featuredImage {
            altText
            url
          }
          variants(first: 10) {
            nodes {
              price {
                amount
                currencyCode
              }
              sku
              title
            }
          }
        }
      }
    }
    collections(query: $searchTerm, first: 10) {
      nodes {
        id
        handle
        title
      }
    }
  }
`;

export const RECENT_SEARCHES_PRODUCTS = gql`
  query MetaObjectsQuery {
    metaobjects(type: "app_search_suggestions", first: 10) {
      nodes {
        fields {
          key
          value
          references(first: 10) {
            nodes {
              ... on Product {
                id
                title
                handle
                tags
                featuredImage {
                  altText
                  height
                  src
                  url
                }
                priceRange {
                  maxVariantPrice {
                    amount
                    currencyCode
                  }
                  minVariantPrice {
                    amount
                    currencyCode
                  }
                }
                variants(first: 10) {
                  nodes {
                    id
                    title
                    image {
                      url
                      altText
                    }
                    price {
                      amount
                      currencyCode
                    }
                  }
                }
              }
              ... on Collection {
                id
                handle
                title
                image {
                  url
                }
              }
              ... on MediaImage {
                image {
                  url
                  altText
                  width
                  height
                }
              }
            }
          }
          reference {
            ... on Collection {
              handle
            }
            ... on MediaImage {
              id
              image {
                altText
                src
                url
              }
            }
          }
        }
      }
    }
  }
`;

export const MEDIA_IMAGE_BY_ID = gql`
  query MediaImageById($id: ID!) {
    node(id: $id) {
      ... on MediaImage {
        image {
          url
          altText
          width
          height
        }
      }
    }
  }
`;
