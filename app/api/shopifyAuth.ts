import * as Crypto from 'expo-crypto';
import { useState } from 'react';
import { <PERSON><PERSON>, Linking } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Buffer } from 'buffer';

const SHOPIFY_STORE_URL = 'https://sunrise-trade.myshopify.com';
const CLIENT_ID = 'shp_14fe4f79-cc74-42bb-8f31-e072f2ff10d4';
const CLIENT_SECRET = 'shpss_14fe4f79-cc74-42bb-8f31-e072f2ff10d4';
const REDIRECT_URI = 'shop.***********.app://auth/callback';
const CUSTOMER_ACCOUNT_API_URL = `https://shopify.com/***********/account/customer/api/2024-01/graphql`;
const SHOPIFY_SHOP_ID = '***********';

// Define required scopes for Customer Account API
const REQUIRED_SCOPES = [
  'openid',
  'email',
  'customer-account-api:full'
].join(' ');

// Generate a random code verifier
async function generateCodeVerifier() {
  const randomBytes = await Crypto.getRandomBytesAsync(32);
  return Buffer.from(randomBytes).toString('base64url');
}

// Generate code challenge from verifier
async function generateCodeChallenge(codeVerifier: string) {
  const hashed = await Crypto.digestStringAsync(
    Crypto.CryptoDigestAlgorithm.SHA256,
    codeVerifier
  );
  return Buffer.from(hashed, 'hex').toString('base64url');
}

// Format token with shcat_ prefix if needed
function formatToken(token: string): string {
  // Remove any existing prefix
  const cleanToken = token.replace(/^shcat_/, "");
  // Add the prefix
  return `shcat_${cleanToken}`;
}

// Decode JWT token to check expiration and permissions
function decodeJWT(token: string) {
  try {
    const base64Url = token.split(".")[1];
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
    const jsonPayload = decodeURIComponent(
      Buffer.from(base64, "base64")
        .toString()
        .split("")
        .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
        .join("")
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error("❌ Error decoding JWT:", error);
    return null;
  }
}

// Check if token is expired
function isTokenExpired(token: string): boolean {
  const decoded = decodeJWT(token);
  if (!decoded) return true;

  const currentTime = Math.floor(Date.now() / 1000);
  return decoded.exp < currentTime;
}

// Check if token has required permissions
function hasRequiredPermissions(token: string): boolean {
  const decoded = decodeJWT(token);
  if (!decoded) return false;

  const requiredScopes = [
    "openid",
    "email",
    "customer-account-api:full",
  ];

  const tokenScopes = decoded.scope.split(" ");
  return requiredScopes.every((scope) => tokenScopes.includes(scope));
}

// Exchange authorization code for access token
async function exchangeCodeForToken(code: string, codeVerifier: string) {
  try {
    console.log('🔄 Exchanging code for token...');

    const response = await fetch(`https://shopify.com/authentication/${SHOPIFY_SHOP_ID}/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: CLIENT_ID,
        redirect_uri: REDIRECT_URI,
        code: code,
        code_verifier: codeVerifier,
      }).toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Token exchange failed:', errorText);
      throw new Error(`Token exchange failed: ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Token exchange successful');

    return {
      access_token: data.access_token,
      refresh_token: data.refresh_token,
      id_token: data.id_token,
      expires_in: data.expires_in,
    };
  } catch (error) {
    console.error('❌ Token Exchange Error:', error);
    throw error;
  }
}

// Refresh access token when it expires
async function refreshAccessToken(refreshToken: string) {
  try {
    console.log('🔄 Refreshing access token...');

    const response = await fetch(`https://shopify.com/authentication/${SHOPIFY_SHOP_ID}/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        client_id: CLIENT_ID,
        refresh_token: refreshToken,
      }).toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Token refresh failed:', errorText);
      throw new Error(`Token refresh failed: ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Token refresh successful');

    return {
      access_token: data.access_token,
      refresh_token: data.refresh_token,
      expires_in: data.expires_in,
    };
  } catch (error) {
    console.error('❌ Token refresh error:', error);
    throw error;
  }
}

// Fetch user information using the access token
async function fetchCustomerData(accessToken: string) {
  try {
    console.log('🔍 Fetching customer data...');

    // Format token with shcat_ prefix
    const formattedToken = formatToken(accessToken);

    // Create request body
    const requestBody = {
      query: `
        {
          customer {
            emailAddress {
              emailAddress
            }
            id
            firstName
            lastName
          }
        }
      `,
      variables: {},
    };

    // Create headers with formatted token
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': formattedToken,
      'Origin': SHOPIFY_STORE_URL,
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    };

    const response = await fetch(CUSTOMER_ACCOUNT_API_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);
      throw new Error(`API request failed with status ${response.status}: ${errorText}`);
    }

    const data = await response.json();

    if (data.errors) {
      console.error('❌ GraphQL Errors:', JSON.stringify(data.errors, null, 2));
      throw new Error(data.errors[0].message || 'Failed to fetch customer data');
    }

    if (!data.data?.customer) {
      console.error('❌ No customer data found. Response:', JSON.stringify(data, null, 2));
      throw new Error('No customer data found');
    }

    // Extract and format the customer ID
    const customerId = data.data.customer.id;
    const formattedCustomerId = customerId.split('/').pop(); // This will get the last part of the ID

    // Add the formatted ID to the response
    data.data.customer.formattedId = formattedCustomerId;

    console.log('✅ Customer data fetched successfully');
    return data;
  } catch (error) {
    console.error('❌ Error fetching customer data:', error);
    throw error;
  }
}

export function useShopifyAuth() {
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState(null);

  // Check if the user is already logged in
  const checkLoginStatus = async () => {
    try {
      const accessToken = await AsyncStorage.getItem('access_token');
      const refreshToken = await AsyncStorage.getItem('refresh_token');

      if (!accessToken) {
        return false;
      }

      // Check if token is expired
      if (isTokenExpired(accessToken)) {
        console.log('🔄 Access token expired, attempting to refresh...');

        // If we have a refresh token, try to get a new access token
        if (refreshToken) {
          try {
            const newTokens = await refreshAccessToken(refreshToken);
            await AsyncStorage.setItem('access_token', newTokens.access_token);

            if (newTokens.refresh_token) {
              await AsyncStorage.setItem('refresh_token', newTokens.refresh_token);
            }

            // Fetch user data with new token
            const userData = await fetchCustomerData(newTokens.access_token);
            setUser(userData.data.customer);
            return true;
          } catch (refreshError) {
            console.error('❌ Token refresh failed:', refreshError);
            // If refresh fails, we need to re-authenticate
            return false;
          }
        } else {
          // No refresh token, need to re-authenticate
          return false;
        }
      }

      // Token is valid, fetch user data
      try {
        const userData = await fetchCustomerData(accessToken);
        setUser(userData.data.customer);
        return true;
      } catch (error) {
        console.error('❌ Error fetching user data with existing token:', error);
        return false;
      }
    } catch (error) {
      console.error('❌ Error checking login status:', error);
      return false;
    }
  };

  const initiateLogin = async () => {
    setIsLoading(true);
    try {
      console.log('🔑 Initiating login...');

      // Generate PKCE values
      const codeVerifier = await generateCodeVerifier();
      const codeChallenge = await generateCodeChallenge(codeVerifier);

      // Generate state for CSRF protection
      const state = await generateCodeVerifier();

      // Generate nonce for replay protection
      const nonce = await generateCodeVerifier();

      // Store values for later verification
      await AsyncStorage.setItem('code_verifier', codeVerifier);
      await AsyncStorage.setItem('oauth_state', state);
      await AsyncStorage.setItem('oauth_nonce', nonce);

      // Construct authorization URL
      const authUrl = new URL(`https://shopify.com/authentication/${SHOPIFY_SHOP_ID}/oauth/authorize`);
      authUrl.searchParams.append('client_id', CLIENT_ID);
      authUrl.searchParams.append('redirect_uri', REDIRECT_URI);
      authUrl.searchParams.append('response_type', 'code');
      authUrl.searchParams.append('scope', REQUIRED_SCOPES);
      authUrl.searchParams.append('code_challenge', codeChallenge);
      authUrl.searchParams.append('code_challenge_method', 'S256');
      authUrl.searchParams.append('state', state);
      authUrl.searchParams.append('nonce', nonce);

      console.log('🔑 Auth URL:', authUrl.toString());

      // Open the authorization URL
      await Linking.openURL(authUrl.toString());
      return authUrl.toString();
    } catch (error) {
      console.error('❌ Login initiation error:', error);
      Alert.alert('Error', 'Failed to initiate login');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeepLink = async (url: string) => {
    try {
      console.log('🔗 Handling deep link:', url);

      const urlObj = new URL(url);
      const code = urlObj.searchParams.get('code');
      const state = urlObj.searchParams.get('state');

      if (!code) {
        throw new Error('No authorization code received');
      }

      // Verify state to prevent CSRF
      const storedState = await AsyncStorage.getItem('oauth_state');
      if (state !== storedState) {
        throw new Error('Invalid state parameter');
      }

      // Retrieve stored code verifier
      const codeVerifier = await AsyncStorage.getItem('code_verifier');
      if (!codeVerifier) {
        throw new Error('No code verifier found');
      }

      // Exchange code for token
      const tokenData = await exchangeCodeForToken(code, codeVerifier);

      // Store the tokens
      await AsyncStorage.setItem('access_token', tokenData.access_token);
      await AsyncStorage.setItem('refresh_token', tokenData.refresh_token);
      await AsyncStorage.setItem('id_token', tokenData.id_token || '');
      await AsyncStorage.setItem('isLoggedIn', 'true');

      // Calculate token expiration time
      if (tokenData.expires_in) {
        const expiresAt = Date.now() + tokenData.expires_in * 1000;
        await AsyncStorage.setItem('token_expires_at', expiresAt.toString());
      }

      // Fetch user information
      const userData = await fetchCustomerData(tokenData.access_token);

      // Store customer data
      await AsyncStorage.setItem('customerData', JSON.stringify(userData));

      if (userData?.data?.customer) {
        const email = userData.data.customer.emailAddress?.emailAddress;
        const customerId = userData.data.customer.formattedId;

        if (email) {
          await AsyncStorage.setItem('customerEmail', email);
        }

        if (customerId) {
          await AsyncStorage.setItem('accountNumber', customerId);
        }

        setUser(userData.data.customer);
      }

      return {
        token: tokenData.access_token,
        user: userData.data.customer,
        tokenExpiresIn: tokenData.expires_in
      };
    } catch (error) {
      console.error('❌ Deep link handling error:', error);
      Alert.alert('Error', 'Failed to complete login process');
      throw error;
    }
  };

  const logout = async () => {
    try {
      console.log('🚪 Logging out...');

      // Clear all auth-related items from storage
      const keysToRemove = [
        'access_token',
        'refresh_token',
        'id_token',
        'code_verifier',
        'oauth_state',
        'oauth_nonce',
        'token_expires_at',
        'isLoggedIn',
        'customerToken',
        'customerData',
        'customerEmail',
        'accountNumber'
      ];

      await Promise.all(keysToRemove.map(key => AsyncStorage.removeItem(key)));

      setUser(null);
      console.log('✅ Logout successful');

      return true;
    } catch (error) {
      console.error('❌ Logout error:', error);
      Alert.alert('Error', 'Failed to logout');
      return false;
    }
  };

  return {
    isLoading,
    user,
    initiateLogin,
    handleDeepLink,
    logout,
    checkLoginStatus,
    fetchCustomerData,
    refreshAccessToken,
  };
}