import { gql } from "@apollo/client";

export const COMPANY_LOCATION_QUERY = `
  query getCustomerCompanyAndLocation($customerId: ID!) {
    customer(id: $customerId) {
      companyContactProfiles {
        company {
          id
          locations(first: 10) {
            edges {
              node {
                id
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_CUSTOMER_CONTACTS = gql`
  query getCustomerContacts($customerId: ID!) {
    customer(id: $customerId) {
      companyContactProfiles {
        id
        isMainContact
        company {
          id
          name
        }
      }
    }
  }
`;
