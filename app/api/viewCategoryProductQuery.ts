import { gql } from "@apollo/client";

export const GET_SINGLE_PRODUCT = gql`
  query GetProductByHandle($handle: String!) {
    productByHandle(handle: $handle) {
      id
      title
      description
      handle
      images(first: 5) {
        edges {
          node {
            src
            altText
          }
        }
      }
      variants(first: 5) {
        edges {
          node {
            id
            title
            price {
              amount
              currencyCode
            }
            image {
              src
            }
          }
        }
      }
    }
  }
`;
