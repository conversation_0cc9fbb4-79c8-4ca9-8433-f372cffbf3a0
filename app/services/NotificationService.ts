import messaging from "@react-native-firebase/messaging";
import { Platform, Alert, PermissionsAndroid } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { EventEmitter } from "events";
import { Notifications } from "react-native-notifications";

class NotificationService {
  // Event emitter for notifications
  private eventEmitter: EventEmitter;
  private pushNotificationConfigured: boolean = false;

  // Backend configuration for real system notifications
  private readonly BACKEND_URL = this.getBackendUrl();

  private getBackendUrl(): string {
    // For development with Serveo (recommended - no signup required!)
    const SERVEO_URL = 'https://e56d8d9cc9b84c1327fe1b18ca65380c.serveo.net'; // TODO: Replace with your serveo URL

    // Alternative: ngrok (requires signup)
    const NGROK_URL = 'https://abc123.ngrok.io'; // TODO: Replace with your ngrok URL

    // Alternative development options
    const DEV_IP = "***********"; // Your computer's IP address (run: node scripts/find-ip.js)
    const DEV_PORT = "3000";
    const ANDROID_EMULATOR_IP = "********"; // Special IP for Android emulator

    // For production - replace with your deployed server URL
    const PROD_URL = "https://your-backend-url.com";

    // Environment-based URL selection
    // if (__DEV__) {
      // Option 1: Use Serveo (recommended - no signup required!)
      return SERVEO_URL;

      // Option 2: Use ngrok (uncomment if using ngrok instead)
      // return NGROK_URL;

      // Option 3: Use Android emulator IP (uncomment if not using tunneling)
      // return `http://${ANDROID_EMULATOR_IP}:${DEV_PORT}`;

      // Option 4: Use your computer's IP (uncomment if not using tunneling)
      // return `http://${DEV_IP}:${DEV_PORT}`;
    // } else {
    //   // Production: Use your deployed server URL
    //   return PROD_URL;
    // }
  }

  constructor() {
    this.eventEmitter = new EventEmitter();
    this.configurePushNotification();
  }

  // Configure notifications for real system notifications
  private configurePushNotification() {
    if (this.pushNotificationConfigured) return;

    try {
      // Initialize react-native-notifications
      Notifications.registerRemoteNotifications();

      // Create notification channel for Android
      if (Platform.OS === 'android') {
        this.createNotificationChannel();
      }

      // Set up notification event listeners
      Notifications.events().registerNotificationReceivedForeground(
        (notification, completion) => {
          console.log("📱 Notification received in foreground:", notification);
          completion({ alert: true, sound: true, badge: false });
        }
      );

      Notifications.events().registerNotificationOpened(
        (notification, completion) => {
          console.log("📱 Notification opened:", notification);
          completion();
        }
      );

      // Request permissions for both platforms
      this.requestNotificationPermissions();

      this.pushNotificationConfigured = true;
      console.log("✅ Notifications configured successfully");
    } catch (error) {
      console.error("❌ Error configuring notifications:", error);
    }
  }

  // Create notification channel for Android
  private createNotificationChannel() {
    if (Platform.OS === 'android') {
      try {
        console.log('📱 Creating Android notification channel...');

        // Note: react-native-notifications handles channels automatically
        // We'll specify the channel when posting notifications
        console.log('✅ Android notification channels will be created automatically');
      } catch (error) {
        console.error('❌ Error creating notification channels:', error);
      }
    }
  }

  // Request notification permissions
  private async requestNotificationPermissions() {
    try {
      if (Platform.OS === 'android') {
        console.log('📱 Requesting Android notification permissions...');

        // For Android 13+ (API level 33+), we need to request POST_NOTIFICATIONS permission
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
            {
              title: 'Notification Permission',
              message: 'This app needs access to show notifications',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            }
          );

          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            console.log('✅ Android notification permission granted');
          } else {
            console.log('❌ Android notification permission denied');
          }
        } else {
          console.log('✅ Android notification permissions not required for this version');
        }
      } else {
        console.log('📱 iOS permissions will be requested by Firebase messaging');
      }
    } catch (error) {
      console.error('❌ Error requesting notification permissions:', error);
    }
  }

  // Subscribe to notification events
  onNotification(callback: (notification: any) => void) {
    this.eventEmitter.on("notification", callback);
    return () => this.eventEmitter.off("notification", callback);
  }

  // Initialize messaging service
  async initialize() {
    try {
      console.log("🔔 Initializing notification service...");

      // Check if Firebase messaging is available
      if (!messaging) {
        console.error("❌ Firebase messaging is not available");
        return false;
      }

      // Request permission for iOS (Android doesn't need this explicit request)
      if (Platform.OS === "ios") {
        console.log(
          "📱 iOS device detected, requesting notification permissions..."
        );
        const authStatus = await messaging().requestPermission();
        console.log("📱 iOS permission status:", authStatus);

        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (!enabled) {
          console.log("❌ Notification permission not granted on iOS");
          return false;
        }
        console.log("✅ iOS notification permissions granted");
      } else {
        console.log(
          "📱 Android device detected, checking notification permissions..."
        );
        // On Android, we can check the current permission status
        const enabled = await messaging().hasPermission();
        console.log("📱 Android permission status:", enabled);

        if (
          enabled === messaging.AuthorizationStatus.NOT_DETERMINED ||
          enabled === messaging.AuthorizationStatus.DENIED
        ) {
          // Request permissions on Android (this is optional but recommended)
          const authStatus = await messaging().requestPermission();
          console.log("📱 Android permission request result:", authStatus);

          if (authStatus === messaging.AuthorizationStatus.DENIED) {
            console.log("❌ Notification permission denied on Android");
            return false;
          }
        }
        console.log(
          "✅ Android notification permissions granted or already available"
        );
      }

      // Get FCM token
      const fcmToken = await this.getFcmToken();
      console.log("📝 FCM Token obtained:", fcmToken);

      // Set up foreground message handler
      this.setupForegroundHandler();
      console.log("✅ Foreground message handler set up");

      // Set up background message handler
      messaging().setBackgroundMessageHandler(async (remoteMessage) => {
        console.log("📬 Background message received:", remoteMessage);

        // Show notification in system tray when app is in background
        if (remoteMessage.notification) {
          await this.showSystemNotification(
            remoteMessage.notification.title || 'New Notification',
            remoteMessage.notification.body || '',
            remoteMessage.data || {}
          );
        }

        return this.handleMessage(remoteMessage);
      });
      console.log("✅ Background message handler set up");

      console.log("🎉 Notification service initialized successfully");
      return true;
    } catch (error) {
      console.error("❌ Error initializing notifications:", error);
      return false;
    }
  }

  // Get FCM token and store it
  async getFcmToken() {
    try {
      console.log("🔑 Getting FCM token...");

      // Check if we already have a token
      let fcmToken = await AsyncStorage.getItem("fcmToken");

      if (fcmToken) {
        console.log("🔑 Found existing FCM token in storage");

        // Verify if the token is still valid by getting a fresh one and comparing
        try {
          const freshToken = await messaging().getToken();
          if (freshToken !== fcmToken) {
            console.log("🔄 Token has changed, updating stored token");
            fcmToken = freshToken;
            await AsyncStorage.setItem("fcmToken", fcmToken);
            // Send updated token to server
            await this.sendTokenToServer(fcmToken);
          }
        } catch (tokenError) {
          console.error(
            "⚠️ Error verifying token, will use stored token:",
            tokenError
          );
        }
      } else {
        console.log("🔑 No token found in storage, generating new token");
        // Get new token
        fcmToken = await messaging().getToken();

        if (fcmToken) {
          // Save the token
          await AsyncStorage.setItem("fcmToken", fcmToken);
          console.log("✅ New FCM Token generated and saved");

          // Send this token to your backend
          await this.sendTokenToServer(fcmToken);

          // Mark that this is a new installation
          const isFirstInstall = await AsyncStorage.getItem("isFirstInstall");
          if (!isFirstInstall) {
            await AsyncStorage.setItem("isFirstInstall", "false");
            await this.sendWelcomeNotification();
          }
        } else {
          console.error("❌ Failed to generate FCM token");
        }
      }

      // Log the token for debugging
      if (fcmToken) {
        console.log("📝 FCM Token:", fcmToken);
      }

      return fcmToken;
    } catch (error) {
      console.error("❌ Error getting FCM token:", error);
      return null;
    }
  }

  // Send FCM token to your backend server
  async sendTokenToServer(token: string) {
    try {
      console.log("📤 Sending FCM token to server...");

      // Get user data if available
      const customerData = await AsyncStorage.getItem("customerData");

      let userId = null;
      if (customerData) {
        try {
          const parsedData = JSON.parse(customerData);
          userId = parsedData?.data?.customer?.id;
        } catch (e) {
          console.warn("Could not parse customer data");
        }
      }

      // Prepare the payload
      const payload = {
        fcmToken: token,
        platform: Platform.OS,
        userId: userId,
        timestamp: new Date().toISOString(),
        appVersion: "1.0.0", // You can get this from your app config
      };

      console.log("📤 Token payload:", payload);

      // TODO: Replace with your actual backend endpoint
      // const response = await fetch('YOUR_BACKEND_URL/api/register-fcm-token', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     ...(customerToken && { 'Authorization': `Bearer ${customerToken}` }),
      //   },
      //   body: JSON.stringify(payload),
      // });

      // if (response.ok) {
      //   console.log('✅ FCM token sent to server successfully');
      //   await AsyncStorage.setItem('tokenSentToServer', 'true');
      // } else {
      //   console.error('❌ Failed to send FCM token to server:', response.status);
      // }

      // For now, just log that we would send it
      console.log(
        "✅ FCM token ready to be sent to server (implement your backend endpoint)"
      );
      await AsyncStorage.setItem("tokenSentToServer", "true");
    } catch (error) {
      console.error("❌ Error sending FCM token to server:", error);
    }
  }

  // Send welcome notification for new installations
  async sendWelcomeNotification() {
    try {
      console.log("🎉 Sending welcome notification for new installation...");

      // Show a system welcome notification
      await this.showSystemNotification(
        "Welcome to Sunrise B2B! 🌅",
        "Thank you for installing our app. Get ready to explore amazing B2B opportunities!",
        {
          type: "welcome",
          action: "open_app",
        }
      );

      console.log("✅ Welcome notification sent");
    } catch (error) {
      console.error("❌ Error sending welcome notification:", error);
    }
  }

  // Set up foreground notification handler
  setupForegroundHandler() {
    console.log("🔔 Setting up foreground notification handler");
    return messaging().onMessage(async (remoteMessage) => {
      console.log("📬 Foreground Message received:", remoteMessage);

      // Show notification even when app is in foreground
      if (remoteMessage.notification) {
        const notificationTitle = remoteMessage.notification.title || "New Notification";
        const notificationBody = remoteMessage.notification.body || "";

        try {
          console.log(`🔔 Showing foreground notification: ${notificationTitle} - ${notificationBody}`);

          // Show system notification in notification tray
          await this.showSystemNotification(
            notificationTitle,
            notificationBody,
            remoteMessage.data || {}
          );

        } catch (error) {
          console.error("❌ Error showing foreground notification:", error);
        }
      }

      // Handle the message here
      return this.handleMessage(remoteMessage);
    });
  }

  // Handle background messages
  async handleBackgroundMessage(remoteMessage: any) {
    console.log("📬 Background Message received:", remoteMessage);
    // Handle the message here
    return this.handleMessage(remoteMessage);
  }

  // Common message handler for both foreground and background
  async handleMessage(remoteMessage: any) {
    // Store the notification in AsyncStorage to display in the notifications screen
    try {
      console.log(
        "📩 Handling notification message:",
        JSON.stringify(remoteMessage, null, 2)
      );

      // Get existing notifications
      const existingNotifications = await AsyncStorage.getItem("notifications");
      console.log("📋 Retrieved existing notifications from storage");

      let notifications = [];
      try {
        notifications = existingNotifications
          ? JSON.parse(existingNotifications)
          : [];
        console.log(`📋 Found ${notifications.length} existing notifications`);
      } catch (parseError) {
        console.error(
          "❌ Error parsing existing notifications, resetting:",
          parseError
        );
        notifications = [];
      }

      // Add the new notification
      const newNotification = {
        id: remoteMessage.messageId || `notification-${Date.now()}`,
        title: remoteMessage.notification?.title || "New Notification",
        body: remoteMessage.notification?.body || "",
        data: remoteMessage.data || {},
        date: new Date().toISOString(),
        read: false,
      };

      console.log("✨ Created new notification object:", newNotification);

      // Add to the beginning of the array
      notifications = [newNotification, ...notifications];

      // Save the updated notifications
      await AsyncStorage.setItem(
        "notifications",
        JSON.stringify(notifications)
      );
      console.log("💾 Saved updated notifications to storage");

      // You can also emit an event here to update the UI if the app is in the foreground
      console.log("✅ Notification processing complete");

      return Promise.resolve();
    } catch (error) {
      console.error("❌ Error handling notification:", error);
      return Promise.reject(error);
    }
  }

  // Get all stored notifications
  async getNotifications() {
    try {
      const notifications = await AsyncStorage.getItem("notifications");
      return notifications ? JSON.parse(notifications) : [];
    } catch (error) {
      console.error("Error getting notifications:", error);
      return [];
    }
  }

  // Mark a notification as read
  async markAsRead(notificationId: string) {
    try {
      const existingNotifications = await AsyncStorage.getItem("notifications");
      if (!existingNotifications) return;

      let notifications = JSON.parse(existingNotifications);

      // Find and update the notification
      notifications = notifications.map((notification: any) => {
        if (notification.id === notificationId) {
          return { ...notification, read: true };
        }
        return notification;
      });

      // Save the updated notifications
      await AsyncStorage.setItem(
        "notifications",
        JSON.stringify(notifications)
      );
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  }

  // Delete a notification
  async deleteNotification(notificationId: string) {
    try {
      const existingNotifications = await AsyncStorage.getItem("notifications");
      if (!existingNotifications) return;

      let notifications = JSON.parse(existingNotifications);

      // Filter out the notification to delete
      notifications = notifications.filter(
        (notification: any) => notification.id !== notificationId
      );

      // Save the updated notifications
      await AsyncStorage.setItem(
        "notifications",
        JSON.stringify(notifications)
      );
    } catch (error) {
      console.error("Error deleting notification:", error);
    }
  }

  // Show a local notification (in-app only)
  async showLocalNotification(title: string, body: string, data: any = {}) {
    try {
      console.log("📱 Showing local notification:", title, body);

      // Create a notification object
      const notification = {
        id: `local-${Date.now()}`,
        title,
        body,
        data,
        date: new Date().toISOString(),
        read: false,
      };

      // Store the notification
      await this.handleMessage({
        messageId: notification.id,
        notification: {
          title,
          body,
        },
        data,
      });

      // Emit the notification event
      this.eventEmitter.emit("notification", notification);

      return notification;
    } catch (error) {
      console.error("❌ Error showing local notification:", error);
      return null;
    }
  }

  // Show a system notification (appears in notification tray)
  async showSystemNotification(title: string, body: string, data: any = {}) {
    try {
      console.log("🔔 Showing system notification:", title, body);

      const notificationId = Date.now();

      if (Platform.OS === 'android') {
        // Use native Android notification for guaranteed system tray display
        const { NativeModules } = require('react-native');

        try {
          // Try to use our custom native Android notification module
          if (NativeModules.NotificationModule) {
            const notificationData = {
              id: notificationId,
              title: title,
              body: body,
              channelId: 'high-priority',
            };

            console.log("📱 Using native Android notification module:", notificationData);
            await NativeModules.NotificationModule.showNotification(notificationData);
            console.log("✅ Native Android notification sent successfully");
          } else {
            throw new Error("Native notification module not available");
          }

        } catch (nativeError) {
          console.log("⚠️ Native notification failed, using react-native-notifications:", nativeError);

          // Fallback to react-native-notifications
          const notificationPayload: any = {
            identifier: notificationId.toString(),
            title: title,
            body: body,
            sound: "default",
            badge: 1,
            payload: data,
            category: "sunrise_notifications",
            android: {
              channelId: 'high-priority',
              priority: 'high',
              autoCancel: true,
              color: '#FF4081',
              vibrate: true,
              lights: true,
              ongoing: false,
              showWhen: true,
              when: Date.now(),
            }
          };

          console.log("📱 Posting fallback notification:", notificationPayload);
          Notifications.postLocalNotification(notificationPayload);
        }
      } else {
        // iOS notification
        const notificationPayload: any = {
          identifier: notificationId.toString(),
          title: title,
          body: body,
          sound: "default",
          badge: 1,
          payload: data,
          category: "sunrise_notifications",
        };

        console.log("📱 Posting iOS notification:", notificationPayload);
        Notifications.postLocalNotification(notificationPayload);
      }

      // Also store it for in-app display
      const notification = {
        id: `system-${notificationId}`,
        title,
        body,
        data,
        date: new Date().toISOString(),
        read: false,
      };

      // Store the notification
      await this.handleMessage({
        messageId: notification.id,
        notification: {
          title,
          body,
        },
        data,
      });

      // Emit the notification event
      this.eventEmitter.emit("notification", notification);

      console.log("✅ System notification sent successfully");
      return notification;
    } catch (error) {
      console.error("❌ Error showing system notification:", error);
      return null;
    }
  }

  // Send notification via backend server (for real system notifications)
  async sendNotificationViaBackend(notificationData: {
    token: string;
    title: string;
    body: string;
    data?: any;
  }) {
    try {
      console.log(
        "🔥 Sending notification via backend:",
        notificationData.title
      );

      const response = await fetch(
        `${this.BACKEND_URL}/api/send-notification`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            // Add your authentication headers here
            // 'Authorization': 'Bearer YOUR_API_KEY',
          },
          body: JSON.stringify({
            token: notificationData.token,
            notification: {
              title: notificationData.title,
              body: notificationData.body,
            },
            data: {
              ...notificationData.data,
              timestamp: new Date().toISOString(),
            },
            android: {
              priority: "high",
              notification: {
                title: notificationData.title,
                body: notificationData.body,
                sound: "default",
                color: "#FF4081",
              },
            },
            apns: {
              payload: {
                aps: {
                  alert: {
                    title: notificationData.title,
                    body: notificationData.body,
                  },
                  sound: "default",
                  badge: 1,
                },
              },
            },
          }),
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Backend notification sent successfully:", result);
        return result;
      } else {
        throw new Error(`Backend responded with status: ${response.status}`);
      }
    } catch (error) {
      console.error("❌ Error sending notification via backend:", error);
      throw error;
    }
  }

  // Send a real system notification via Firebase (requires backend implementation)
  async sendFirebaseNotification(
    title: string,
    body: string,
    data: any = {},
    targetToken?: string
  ) {
    try {
      console.log("🔥 Sending Firebase notification:", title, body);

      // Get FCM token if not provided
      const fcmToken = targetToken || (await this.getFcmToken());

      if (!fcmToken) {
        console.error("❌ No FCM token available for sending notification");
        return null;
      }

      // Try to send via backend first for real system notifications
      try {
        await this.sendNotificationViaBackend({
          token: fcmToken,
          title: title,
          body: body,
          data: data,
        });

        console.log("✅ Real system notification sent via backend");
        return { success: true, method: "backend" };
      } catch (backendError) {
        console.warn(
          "⚠️ Backend notification failed, falling back to local notification:",
          backendError
        );

        // Fallback to local notification
        await this.showSystemNotification(title, body, data);
        return { success: true, method: "local_fallback" };
      }
    } catch (error) {
      console.error("❌ Error sending Firebase notification:", error);
      return null;
    }
  }

  // Show both local and system notification
  async showNotification(
    title: string,
    body: string,
    data: any = {},
    systemNotification: boolean = true
  ) {
    try {
      if (systemNotification) {
        return await this.showSystemNotification(title, body, data);
      } else {
        return await this.showLocalNotification(title, body, data);
      }
    } catch (error) {
      console.error("❌ Error showing notification:", error);
      return null;
    }
  }

  // Handle user login success - re-register notifications with user context
  async onLoginSuccess(userData: any) {
    try {
      console.log(
        "🔐 User logged in successfully, updating notification registration..."
      );

      // Get current FCM token
      const fcmToken = await this.getFcmToken();

      if (fcmToken) {
        // Send token to server with user context
        await this.sendTokenToServer(fcmToken);

        // Send login success notification via Firebase for real system notification
        await this.sendFirebaseNotification(
          "Login Successful! 🎉",
          "Welcome back! You're now logged in to Sunrise B2B.",
          {
            type: "login_success",
            userId: userData?.id || userData?.data?.customer?.id,
            action: "open_dashboard",
            timestamp: new Date().toISOString(),
          }
        );
      }

      console.log("✅ Login notification registration complete");
    } catch (error) {
      console.error("❌ Error handling login success notification:", error);
    }
  }

  // Handle app installation/first launch
  async onAppInstall() {
    try {
      console.log("📱 Handling app installation notification setup...");

      const isFirstLaunch = await AsyncStorage.getItem("isFirstLaunch");

      if (!isFirstLaunch) {
        console.log("🎉 First app launch detected!");

        // Mark as not first launch anymore
        await AsyncStorage.setItem("isFirstLaunch", "false");

        // Initialize notifications
        const initialized = await this.initialize();

        if (initialized) {
          // Send welcome notification via Firebase for real system notification
          setTimeout(async () => {
            await this.sendFirebaseNotification(
              "Welcome to Sunrise B2B! 🌅",
              "Discover amazing B2B opportunities. Tap to get started!",
              {
                type: "welcome",
                action: "open_onboarding",
                timestamp: new Date().toISOString(),
              }
            );
          }, 5000); // 5 second delay to ensure FCM token is ready
        }

        console.log("✅ App installation notification setup complete");
      } else {
        console.log(
          "📱 App already launched before, skipping welcome notification"
        );
        // Still initialize notifications for returning users
        await this.initialize();
      }
    } catch (error) {
      console.error("❌ Error handling app installation:", error);
    }
  }

  // Handle company verification success
  async onCompanyVerified() {
    try {
      console.log("🏢 Company verified, sending notification...");

      await this.showSystemNotification(
        "Company Verified! ✅",
        "Your company has been successfully verified. You now have full access to all features.",
        {
          type: "company_verified",
          action: "open_dashboard",
        }
      );

      console.log("✅ Company verification notification sent");
    } catch (error) {
      console.error(
        "❌ Error sending company verification notification:",
        error
      );
    }
  }

  // Get notification statistics
  async getNotificationStats() {
    try {
      const notifications = await this.getNotifications();
      const unreadCount = notifications.filter((n: any) => !n.read).length;

      return {
        total: notifications.length,
        unread: unreadCount,
        read: notifications.length - unreadCount,
      };
    } catch (error) {
      console.error("❌ Error getting notification stats:", error);
      return { total: 0, unread: 0, read: 0 };
    }
  }
}

export default new NotificationService();
