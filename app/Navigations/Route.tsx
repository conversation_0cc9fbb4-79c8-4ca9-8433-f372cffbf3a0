import React, { useEffect, useState } from "react";
import {
  NavigationContainer,
  DefaultTheme as NavigationDefaultTheme,
  DarkTheme as NavigationDarkTheme,
} from "@react-navigation/native";
import StackNavigator from "./StackNavigator";
import themeContext from "../constants/themeContext";
import { COLORS } from "../constants/theme";
import { useDispatch } from "react-redux";
import { setCartId } from "../redux/reducer/cartReducer";
import AsyncStorage from "@react-native-async-storage/async-storage";

const { Provider: ThemeProvider } = themeContext;

const Routes = () => {
  const dispatch = useDispatch();
  const [isDarkTheme, setIsDarkTheme] = useState(false);

  useEffect(() => {
    const getCartId = async () => {
      const id = await AsyncStorage.getItem("cartId");
      if (id !== null && typeof id === "string") {
        dispatch(setCartId(id));
      }
    };
    getCartId();
  }, []);

  const authContext = React.useMemo(
    () => ({
      setDarkTheme: () => {
        setIsDarkTheme(true);
      },
      setLightTheme: () => {
        setIsDarkTheme(false);
      },
    }),
    []
  );

  const CustomDefaultTheme = {
    ...NavigationDefaultTheme,
    colors: {
      ...NavigationDefaultTheme.colors,
      background: COLORS.background,
      title: COLORS.title,
      card: COLORS.card,
      text: COLORS.text,
      textLight: COLORS.textLight,
      input: COLORS.input,
      border: COLORS.borderColor,
    },
  };

  const CustomDarkTheme = {
    ...NavigationDarkTheme,
    colors: {
      ...NavigationDarkTheme.colors,
      background: COLORS.darkBackground,
      title: COLORS.darkTitle,
      card: COLORS.darkCard,
      text: COLORS.darkText,
      textLight: COLORS.darkTextLight,
      input: COLORS.darkInput,
      border: COLORS.darkBorder,
    },
  };

  const theme = isDarkTheme ? CustomDarkTheme : CustomDefaultTheme;

  return (
    <ThemeProvider value={authContext}>
      <NavigationContainer theme={theme}>
        <StackNavigator />
      </NavigationContainer>
    </ThemeProvider>
  );
};

export default Routes;


