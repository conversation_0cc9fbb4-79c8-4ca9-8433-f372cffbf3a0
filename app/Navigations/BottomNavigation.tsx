import React from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import HomeScreen from "../Screens/Home/Home";
import BottomTab from "../layout/BottomTab";
import MyCart from "../Screens/MyCart/MyCart";
import Category from "../Screens/Category/Category";
import Profile from "../Screens/profile/Profile";
import { BottomTabParamList } from "./BottomTabParamList";
import Wishlist from "../Screens/Wishlist/Wishlist";
import { Image } from "react-native";
import Search from "../Screens/search/Search";
import CompanyRegistration from "../Screens/CompanyRegistration/CompanyRegistration";

const Tab = createBottomTabNavigator<BottomTabParamList>();

const BottomNavigation = () => {
  return (
    <Tab.Navigator
      initialRouteName="Home"
      screenOptions={{
        headerShown: false,
      }}
      tabBar={(props: any) => {
        const isCartActive =
          props.state.routes[props.state.index].name === "MyCart";
        return isCartActive ? null : <BottomTab {...props} />;
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        // options={{
        //   headerShown: true,
        //   title: "",
        //   headerLeft: () => (
        //     <Image
        //       source={require("../../assets/Sunrisetradedarklogo.png")}
        //       style={{ width: 150, height: 50, marginHorizontal: 10 }}
        //     />
        //   ),
        //   // headerRight={()=>()}
        // }}
      />
      <Tab.Screen name="Category" component={Category} />
      <Tab.Screen name="Search" component={Search} />
      <Tab.Screen name="MyCart" component={MyCart} />
      <Tab.Screen name="Profile" component={Profile} />
    </Tab.Navigator>
  );
};

export default BottomNavigation;
