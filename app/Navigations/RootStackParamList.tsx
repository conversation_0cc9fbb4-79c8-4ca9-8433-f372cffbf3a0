import { NavigatorScreenParams } from "@react-navigation/native";
import { BottomTabParamList } from "./BottomTabParamList";

export type RootStackParamList = {
  DrawerNavigation: NavigatorScreenParams<BottomTabParamList>;
  SplashScreen:undefined;
  Damo: undefined;
  LoginPage:undefined;
  ShopifyWebViwew:undefined;
  ChooseLanguage: undefined;
  QrCodeScanner: undefined;
  SignUp: undefined;
  SignIn: undefined;
  ShopifyLogin: undefined;
  ShopifyWebView: { onLoginSuccess: (userData: any) => void };
  Onbording: undefined;
  Login: undefined;
  Register: undefined;
  CompanyRegistration: undefined;
  ApplicationSubmitted: undefined;
  ForgatPassword: undefined;
  EnterCode: undefined;
  EmailVerify: undefined;
  NewPassword: undefined;
  ResetPassword: undefined;
  Settings: undefined;
  ChangePassword: undefined;
  TwoStepAuthentication: undefined;
  BottomNavigation: undefined;
  SingleChat: undefined;
  Chat: undefined;
  Support: undefined;
  History: undefined;
  Verification: undefined;
  Call: undefined;
  EditProfile: undefined;
  WriteReview: undefined;
  Trackorder: undefined;
  Products: undefined;
  Language: undefined;
  MyCart: { cartData: any };
  Category: undefined;
  Notifications: undefined;
  Questions: undefined;
  ProductDetails: undefined;
  Writereview: undefined;
  Profile: undefined;
  Wishlist: undefined;
  Search: undefined;
  Components: undefined;
  Coupons: undefined;
  SavedAddresses: undefined;
  Checkout: undefined;
  Addcard: undefined;
  Payment: undefined;
  SaveAddress: undefined;
  Myorder: undefined;
  AddCard: undefined;
  Notification: undefined;
  Home: undefined;
  MainHome: undefined;
  Accordion: undefined;
  ActionModals: undefined;
  BottomSheet: undefined;
  BottomNavModal: undefined;
  ModalBox: undefined;
  Buttons: undefined;
  Badges: undefined;
  Charts: undefined;
  Headers: undefined;
  lists: undefined;
  Pricings: undefined;
  DividerElements: undefined;
  Snackbars: undefined;
  Socials: undefined;
  Swipeable: undefined;
  Tabs: undefined;
  Tables: undefined;
  Toggles: undefined;
  Inputs: undefined;
  Footers: undefined;
  TabStyle1: undefined;
  TabStyle2: undefined;
  TabStyle3: undefined;
  TabStyle4: undefined;
  ProductListingPage: undefined;
  ProductDetailsPage: undefined;
  CardSlider: undefined;
  CardHeader: undefined;
  SingleBannerImage: undefined;
  AddToCartCard: undefined;
  CheckouWebViewPage:undefined
  AuthLoading:undefined
  ApplicationStatus:undefined
  TestNotificationScreen:undefined
};
