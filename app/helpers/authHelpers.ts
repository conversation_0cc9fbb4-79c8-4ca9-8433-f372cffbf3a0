import { encode as base64encode } from 'react-native-base64';
import * as Crypto from 'expo-crypto';

export const generateCodeVerifier = async () => {
  const randomBytes:any = await Crypto.getRandomBytesAsync(32);
  return base64encode(randomBytes).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
};

export const generateCodeChallenge = async (verifier:any) => {
  const hashed = await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.SHA256, verifier);
  return base64encode(hashed).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
};
