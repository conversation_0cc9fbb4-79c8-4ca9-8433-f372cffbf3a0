/**
 * Utility function to send a test notification to a device
 * This is for testing purposes only and should not be used in production
 * In a real app, notifications would be sent from your backend server
 */

// Replace with your Firebase server key from Firebase Console > Project Settings > Cloud Messaging > Server key
const FIREBASE_SERVER_KEY = 'YOUR_FIREBASE_SERVER_KEY';

/**
 * Send a test notification to a device
 * @param {string} fcmToken - The FCM token of the device to send the notification to
 * @param {object} notification - The notification object
 * @param {string} notification.title - The notification title
 * @param {string} notification.body - The notification body
 * @param {object} data - Additional data to send with the notification
 * @returns {Promise} - A promise that resolves when the notification is sent
 */
export const sendTestNotification = async (fcmToken, notification, data = {}) => {
  try {
    console.log('Sending notification to token:', fcmToken);

    const payload = {
      to: fcmToken,
      notification,
      data: {
        ...data,
        // Adding a timestamp to make each notification unique
        timestamp: new Date().getTime().toString()
      },
      priority: 'high',
      content_available: true,
      // For Android
      android: {
        priority: 'high',
        notification: {
          channel_id: 'high-priority',
          priority: 'high',
          default_sound: true,
          default_vibrate_timings: true
        }
      },
      // For iOS
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1,
            content_available: true
          }
        }
      }
    };

    console.log('Notification payload:', JSON.stringify(payload, null, 2));

    const response = await fetch('https://fcm.googleapis.com/fcm/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `key=${FIREBASE_SERVER_KEY}`
      },
      body: JSON.stringify(payload)
    });

    const responseData = await response.json();
    console.log('FCM Response:', responseData);

    if (responseData.success === 0) {
      console.error('Notification failed to send. Error:', responseData.results?.[0]?.error);
    } else {
      console.log('Notification sent successfully!');
    }

    return responseData;
  } catch (error) {
    console.error('Error sending notification:', error);
    throw error;
  }
};

/**
 * Example usage:
 *
 * // First, get the FCM token
 * import AsyncStorage from '@react-native-async-storage/async-storage';
 *
 * const fcmToken = await AsyncStorage.getItem('fcmToken');
 *
 * // Then send a test notification
 * sendTestNotification(
 *   fcmToken,
 *   {
 *     title: 'New Promotion!',
 *     body: 'Check out our latest deals and save up to 50%'
 *   },
 *   {
 *     type: 'promotion',
 *     deepLink: 'app://products/featured'
 *   }
 * );
 */
