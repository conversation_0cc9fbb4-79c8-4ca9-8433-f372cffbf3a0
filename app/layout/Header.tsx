import React from "react";
import { Image, Platform, Text, TouchableOpacity, View } from "react-native";
import { COLORS, FONTS, FONTWEIGHT } from "../constants/theme";
import { Feather } from "@expo/vector-icons";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import { useNavigation, useTheme } from "@react-navigation/native";

import { Appbar, IconButton } from "react-native-paper";
import { GlobalStyleSheet } from "../constants/StyleSheet";
import { IMAGES } from "../constants/Images";

const Header = (props: {
  productId?: any;
  totalItems?: any;
  transparent?: any;
  paddingLeft?: any;
  leftIcon?: any;
  backAction?: any;
  titleLeft?: any;
  title?: any;
  rightIcon2?: any;
  rightIconhandler?: any;
  rightIcon?: any;
  rightIcon3?: any;
  rightIcon4?: any;
  handleLike?: any;
  isLike?: any;
  grid?: any;
  handleLayout?: any;
  layout?: any;
  onPress?: any;
}) => {
  const navigation = useNavigation<any>();

  const theme = useTheme();
  const { colors }: { colors: any } = theme;

  const { grid, handleLayout, layout } = props;

  return (
    <View
      style={[
        {
          shadowColor: "#000",
          shadowOffset: {
            width: 2,
            height: 2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 5,
        },
        Platform.OS === "ios" && {
          backgroundColor: colors.card,
        },
      ]}
    >
      <View
        style={[
          {
            height: props.productId ? 60 : 60,
            backgroundColor: COLORS.background,
          },
          props.transparent && {
            position: "absolute",
            left: 0,
            right: 0,
            borderBottomWidth: 0,
          },
        ]}
      >
        <View
          style={[
            GlobalStyleSheet.container,
            {
              flexDirection: "row",
              alignItems: "center",
              paddingLeft: props.paddingLeft ? 15 : 0,
              justifyContent: "space-between",
              paddingTop: 10,
            },
          ]}
        >
          {props.leftIcon == "back" && (
            // <TouchableOpacity
            //   onPress={() =>
            //     props.backAction ? props.backAction() : navigation.goBack()
            //   }
            // >
            <IconButton
              onPress={() =>
                props.backAction ? props.backAction() : navigation.goBack()
              }
              icon={(props) => (
                <Image
                  {...props}
                  source={IMAGES.goback}
                  style={{ width: 30, height: 30 }}
                />
              )}
              iconColor={colors.title}
              size={15}
            />
            // </TouchableOpacity>
          )}
          {props.leftIcon == "sunriselogo" && (
            // <TouchableOpacity
            //   onPress={() =>
            //     props.backAction ? props.backAction() : navigation.goBack()
            //   }
            // >
            // <IconButton
            //   onPress={() =>
            //     props.backAction ? props.backAction() : navigation.goBack()
            //   }
            //   icon={(props) => (
            <Image
              {...props}
              source={require("../../assets/Sunrisetradedarklogo.png")}
              style={{ width: 170, height: 50 }}
            />
            //   // )}
            //   // iconColor={colors.title}

            // />
            // </TouchableOpacity>
          )}
          {/* {props.leftIcon == "logo" &&
                        <View>
                        <Image source={require("../../assets/Sunrisetradedarklogo.png")} style={{width:150,height:50,marginHorizontal:10}}  /></View>
                    } */}
          <View
            style={{
              flex: 1,
              flexDirection: "row",
              gap: 5,
              alignItems: "center",
            }}
          >
            <Text
              style={{
                ...FONTS.fontMedium,
                fontSize: 18,
                color: colors.title,
                textAlign: props.titleLeft ? "left" : "center",
              }}
            >
              {props.title}
            </Text>
            {props?.totalItems && (
              <Text
                style={{
                  // ...FONTS.fontMedium,
                  fontSize: 14,
                  color: COLORS.label,
                  textAlign: props.titleLeft ? "left" : "center",
                  fontWeight: FONTWEIGHT.Thin,
                }}
              >
                {`(${props.totalItems} items)`}
              </Text>
            )}
            {props.productId && (
              <Text
                style={{
                  ...FONTS.fontSm,
                  color: colors.text,
                  textAlign: "center",
                  marginTop: 2,
                }}
              >
                {props.productId}
              </Text>
            )}
          </View>
          {props.rightIcon2 == "search" && (
            <IconButton
              onPress={() => navigation.navigate("Search")}
              size={20}
              iconColor={colors.title}
              icon={(props) => <Feather name="search" {...props} />}
            />
          )}
          <TouchableOpacity
            onPress={() => {
              navigation.navigate("MyCart");
            }}
          >
            {props.rightIcon == "cart" && (
              <View
                style={[
                  GlobalStyleSheet.notification,
                  { position: "absolute", right: 4, bottom: 26, zIndex: 100 },
                ]}
              >
                <Text
                  style={{
                    ...FONTS.fontRegular,
                    fontSize: 10,
                    color: COLORS.black,
                  }}
                >
                  {10}
                </Text>
              </View>
            )}
            {props.rightIcon == "cart" && (
              <IconButton
                onPress={props.onPress}
                size={20}
                iconColor={colors.title}
                icon={(prop) => (
                  <Image
                    {...prop}
                    style={{
                      height: 20,
                      width: 20,
                      resizeMode: "contain",
                      tintColor: colors.title,
                    }}
                    source={IMAGES.shopping}
                  />
                )}
              />
            )}
          </TouchableOpacity>
          {props.rightIcon3 == "home" && (
            <IconButton
              onPress={() => navigation.navigate("DrawerNavigation")}
              size={20}
              iconColor={colors.title}
              icon={(props) => <Feather name="home" {...props} />}
            />
          )}
          {props.rightIcon4 == "chat" && (
            <IconButton
              onPress={() => navigation.navigate("SingleChat")}
              size={20}
              iconColor={colors.title}
              icon={(props) => (
                <Image
                  {...props}
                  style={{
                    height: 20,
                    width: 20,
                    resizeMode: "contain",
                    tintColor: colors.title,
                  }}
                  source={IMAGES.comment}
                />
              )}
            />
          )}
          {props.rightIcon == "wishlist" && (
            <IconButton
              onPress={() => props.handleLike()}
              size={20}
              iconColor={props.isLike ? "#F9427B" : colors.title}
              icon={(val) => (
                <FontAwesome
                  name={props.isLike ? "heart" : "heart-o"}
                  {...val}
                />
              )}
            />
          )}
          {props.rightIcon == "close" && (
            <TouchableOpacity
              onPress={props.rightIconhandler}
              style={{
                width: "15%",
                height:35,
                alignSelf: "flex-end",
              }}
            >
              <Image
                source={IMAGES.close}
                style={{
                  width: 24,
                  height: 24,
                  position: "absolute",
                  right: -5,
                }}
              />
            </TouchableOpacity>
          )}

          {grid && (
            <View
              style={{
                flexDirection: "row",
              }}
            >
              <TouchableOpacity
                onPress={() => handleLayout("grid")}
                style={{
                  padding: 10,
                }}
              >
                <Image
                  style={{
                    height: 22,
                    width: 22,
                    resizeMode: "contain",
                    tintColor: layout === "grid" ? COLORS.primary : "#BEB9CD",
                  }}
                  source={IMAGES.grid}
                />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleLayout("list")}
                style={{
                  padding: 10,
                }}
              >
                <Image
                  style={{
                    height: 22,
                    width: 22,
                    resizeMode: "contain",
                    tintColor: layout === "list" ? COLORS.primary : "#BEB9CD",
                  }}
                  source={IMAGES.grid2}
                />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default Header;
