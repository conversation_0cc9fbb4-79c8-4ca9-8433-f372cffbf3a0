export const companyDetails = {
  headerTitle: "Company Registration",
  pageDescription:
    "To comply with the e-Invoice implementation by the Malaysia's IRBM/LHDN, we seek your help to input the mandatory info that was required under the e-Invoice implementation.",
  steps: [
    {
      title: "Basic Details",
      input: [
        {
          type: "TEXT",
          label: "Individual Name as per MyKad/passport [for non-malaysian]",
          isMandatory: true,
          valueField: "individualName",
          fieldName: "name",
        },
        {
          type: "TEXT",
          label: "Company Name as per SSM for malaysian",
          isMandatory: true,
          valueField: "companyName",
          fieldName: "companyName",
        },
        {
          type: "EMAIL",
          label: "Email Address(to receive validation of e-invoice from IRBM)",
          isMandatory: false,
          valueField: "emailAddress",
          fieldName: "email",
        },
        {
          type: "NUMBER",
          label: "Business Contact Number",
          isMandatory: true,
          valueField: "businessContactNumber",
          fieldName: "phone",
        },
        {
          type: "TEXT",
          label: "Tax Identification No.(TIN)",
          isMandatory: true,
          valueField: "taxIdentificationNo",
          fieldName: "tin",
        },
      ],
    },
    {
      title: "Business Details",
      input: [
        {
          type: "TEXT",
          label: "Business Registration No. as per SSM for malaysian ",
          isMandatory: true,
          valueField: "businessRegistrationNo",
          fieldLabel: "brn",
        },
        {
          type: "TEXT",
          label: "NRIC No.as per MyKad/passport[for non-malaysian]",
          isMandatory: false,
          valueField: "nricNo",
          fieldLabel: "nricNumber",
        },
        {
          type: "TEXT",
          label: "Old Business Registration No.(as per SSM)",
          isMandatory: false,
          valueField: "oldBusinessRegistrationNo",
          fieldLabel: "obrn",
        },
        {
          type: "NUMBER",
          label: "SST (Sales and Service Tax) Registration No.",
          isMandatory: true,
          valueField: "sstRegistrationNo",
          fieldLabel: "sst",
        },
        {
          type: "TEXT",
          label: "Country ",
          isMandatory: true,
          valueField: "country",
          fieldLabel: "country",
        },
        {
          type: "TEXT",
          label: "State ",
          isMandatory: true,
          valueField: "state",
          fieldLabel: "state",
        },
        {
          type: "TEXT",
          label: "City ",
          isMandatory: true,
          valueField: "city",
          fieldLabel: "city",
        },
        {
          type: "TEXT",
          label: "Zipcode ",
          isMandatory: true,
          valueField: "zipcode",
          fieldLabel: "pincode",
        },
        {
          type: "TEXT",
          label: "Business Address ",
          isMandatory: true,
          valueField: "businessAddress",
          fieldLabel: "businessAddress",
        },
        {
          type: "TEXT",
          label: "Business Activity Description",
          isMandatory: true,
          valueField: "businessActivityDescription",
          fieldLabel: "businessDescription",
        },
        {
          type: "TEXT",
          label: "Malaysia standard industrial classification (MSIC) codes.",
          isMandatory: true,
          valueField: "msicCodes",
          fieldLabel: "msicCodes",
        },
      ],
    },
    {
      title: "Document Required",
      input: [
        {
          type: "Select Company Type",
          label: "Select Company Type",
          isMandatory: true,
          valueField: "individualName",
          isInputDropdown: true,
          options: [
            { label: "Limited Liability Company", value: "company" },
            { label: "Sole Proprietor", value: "Sole Proprietor" },
          ],
        },
      ],
    },
    {
      title: "Review & Submit",
      input: [
        {
          type: "TEXT",
          label: "Individual Name as per MyKad/passport [for non-malaysian]*",
          isMandatory: true,
          valueField: "businessRegistrationNo",
        },
        {
          type: "TEXT",
          label: "Company Name as per SSM for malaysian*",
          isMandatory: false,
          valueField: "nricNo",
        },
        {
          type: "TEXT",
          label: "Email Address(to receive validation of e-invoice from IRBM)",
          isMandatory: false,
          valueField: "oldBusinessRegistrationNo",
        },
        {
          type: "NUMBER",
          label: "Business Contact Number",
          isMandatory: true,
          valueField: "sstRegistrationNo",
        },
        {
          type: "TEXT",
          label: "Tax Identification No.(TIN) ",
          isMandatory: true,
          valueField: "country",
        },
        {
          type: "TEXT",
          label: "Business Registration No. as per SSM for malaysian* ",
          isMandatory: true,
          valueField: "state",
        },
      ],
    },
  ],
};
