{"expo": {"name": "Sunrise", "slug": "Sunrise", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/SunriseLogo.png", "scheme": "sunrise", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/Splashscreenbg.png", "backgroundColor": "#000000"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.sunrisetrade.app", "associatedDomains": ["applinks:sunrise-trade.myshopify.com"]}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/SunriseLogo.png", "backgroundColor": "#000000"}, "package": "com.sunrisetrade.app", "buildType": "apk", "enableProguardInReleaseBuilds": false, "enableHermes": true, "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "sunrise", "host": "home"}], "category": ["BROWSABLE", "DEFAULT"]}], "googleServicesFile": "./google-services.json"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/SunriseLogo.png"}, "plugins": ["expo-router", "expo-secure-store"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "5d467278-7c24-4a08-b5c5-81a4028a0fa7"}}}}