// Debug script to test Firebase notifications

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const serviceAccount = require('./backend-example/fir-analytics-75b8b-firebase-adminsdk-fbsvc-54033cb05b.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

async function testNotification() {
  try {
    console.log('🧪 Testing Firebase notification...');
    
    // Use the token from your logs
    const testToken = 'eHiZyyWXSgmUaaLsLsqh'; // Replace with your full token
    
    const message = {
      token: testToken,
      notification: {
        title: 'Test Notification 🧪',
        body: 'This is a test to see if notifications work!',
      },
      data: {
        type: 'test',
        timestamp: new Date().toISOString(),
      },
      android: {
        priority: 'high',
        notification: {
          title: 'Test Notification 🧪',
          body: 'This is a test to see if notifications work!',
          sound: 'default',
          color: '#FF4081',
        }
      },
      apns: {
        payload: {
          aps: {
            alert: {
              title: 'Test Notification 🧪',
              body: 'This is a test to see if notifications work!',
            },
            sound: 'default',
            badge: 1,
          }
        }
      }
    };

    console.log('📤 Sending test notification...');
    const response = await admin.messaging().send(message);
    console.log('✅ Test notification sent successfully:', response);
    
  } catch (error) {
    console.error('❌ Error sending test notification:', error);
  }
}

// Run the test
testNotification();
