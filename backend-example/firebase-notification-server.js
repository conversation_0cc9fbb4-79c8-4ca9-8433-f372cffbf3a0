// Backend Server Example for Firebase Cloud Messaging
// This is a Node.js/Express server that sends real system notifications

const express = require('express');
const admin = require('firebase-admin');
const cors = require('cors');
const os = require('os');

const app = express();
const PORT = process.env.PORT || 3000;

// Get local IP address for React Native development
function getLocalIPAddress() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

// Middleware
app.use(cors({
  origin: '*', // Allow all origins for development
  methods: ['GET', 'POST'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// Initialize Firebase Admin SDK
// Download your service account key from Firebase Console
// Project Settings > Service Accounts > Generate new private key
const serviceAccount = require('../backend-example/fir-analytics-75b8b-firebase-adminsdk-fbsvc-54033cb05b.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  // Optional: Add your database URL if using Realtime Database
  // databaseURL: 'https://your-project-id-default-rtdb.firebaseio.com'
});

// Endpoint to send notifications
app.post('/api/send-notification', async (req, res) => {
  try {
    const { token, notification, data, android, apns } = req.body;

    if (!token) {
      return res.status(400).json({
        error: 'FCM token is required'
      });
    }

    if (!notification || !notification.title || !notification.body) {
      return res.status(400).json({
        error: 'Notification title and body are required'
      });
    }

    // Prepare the message
    const message = {
      token: token,
      notification: {
        title: notification.title,
        body: notification.body,
      },
      data: data || {},
      android: {
        priority: 'high',
        notification: {
          title: notification.title,
          body: notification.body,
          color: '#FF4081',
          sound: 'default',
          ...android?.notification
        },
        ...android
      },
      apns: {
        payload: {
          aps: {
            alert: {
              title: notification.title,
              body: notification.body,
            },
            sound: 'default',
            badge: 1,
            'content-available': 1,
          }
        },
        ...apns
      }
    };

    console.log('📤 Sending notification:', {
      title: notification.title,
      body: notification.body,
      token: token.substring(0, 20) + '...' // Log partial token for security
    });

    // Send the notification
    const response = await admin.messaging().send(message);

    console.log('✅ Notification sent successfully:', response);

    res.status(200).json({
      success: true,
      messageId: response,
      message: 'Notification sent successfully'
    });

  } catch (error) {
    console.error('❌ Error sending notification:', error);

    res.status(500).json({
      success: false,
      error: error.message,
      code: error.code || 'UNKNOWN_ERROR'
    });
  }
});

// Endpoint to send notifications to multiple tokens
app.post('/api/send-multicast-notification', async (req, res) => {
  try {
    const { tokens, notification, data, android, apns } = req.body;

    if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
      return res.status(400).json({
        error: 'Array of FCM tokens is required'
      });
    }

    if (!notification || !notification.title || !notification.body) {
      return res.status(400).json({
        error: 'Notification title and body are required'
      });
    }

    // Prepare the multicast message
    const message = {
      tokens: tokens,
      notification: {
        title: notification.title,
        body: notification.body,
      },
      data: data || {},
      android: {
        priority: 'high',
        notification: {
          title: notification.title,
          body: notification.body,
          color: '#FF4081',
          sound: 'default',
          ...android?.notification
        },
        ...android
      },
      apns: {
        payload: {
          aps: {
            alert: {
              title: notification.title,
              body: notification.body,
            },
            sound: 'default',
            badge: 1,
            'content-available': 1,
          }
        },
        ...apns
      }
    };

    console.log(`📤 Sending multicast notification to ${tokens.length} devices:`, {
      title: notification.title,
      body: notification.body
    });

    // Send the multicast notification
    const response = await admin.messaging().sendMulticast(message);

    console.log('✅ Multicast notification sent:', {
      successCount: response.successCount,
      failureCount: response.failureCount
    });

    res.status(200).json({
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount,
      responses: response.responses,
      message: 'Multicast notification sent'
    });

  } catch (error) {
    console.error('❌ Error sending multicast notification:', error);

    res.status(500).json({
      success: false,
      error: error.message,
      code: error.code || 'UNKNOWN_ERROR'
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Firebase Notification Server is running',
    timestamp: new Date().toISOString()
  });
});

// Start the server
app.listen(PORT, () => {
  const localIP = getLocalIPAddress();

  console.log(`🚀 Firebase Notification Server running on port ${PORT}`);
  console.log(`\n📱 For React Native development, use these URLs:`);
  console.log(`   Local IP: http://${localIP}:${PORT}/api/send-notification`);
  console.log(`   Health check: http://${localIP}:${PORT}/health`);
  console.log(`\n💻 For web development:`);
  console.log(`   Localhost: http://localhost:${PORT}/api/send-notification`);
  console.log(`   Health check: http://localhost:${PORT}/health`);
  console.log(`\n🔧 Update your React Native app with:`);
  console.log(`   const DEV_IP = '${localIP}';`);
  console.log(`\n💡 Make sure your phone/emulator is on the same WiFi network!`);
});

module.exports = app;
