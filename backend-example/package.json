{"name": "firebase-notification-server", "version": "1.0.0", "description": "Backend server for sending Firebase Cloud Messaging notifications", "main": "firebase-notification-server.js", "scripts": {"start": "node firebase-notification-server.js", "dev": "nodemon firebase-notification-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["firebase", "fcm", "notifications", "push-notifications", "nodejs", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "firebase-admin": "^11.11.0", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}