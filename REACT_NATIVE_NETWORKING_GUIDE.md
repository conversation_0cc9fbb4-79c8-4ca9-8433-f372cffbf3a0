# React Native Networking Guide for System Notifications

## 🚫 **Why localhost doesn't work in React Native**

React Native apps run on a **separate device** (phone/emulator), not on your development machine. When your app tries to connect to `localhost`, it's looking for a server **on the device itself**, not on your computer.

## ✅ **Solutions for Different Scenarios**

### **1. Physical Device (Recommended for Testing)**

**Use your computer's WiFi IP address:**

```bash
# Find your IP address
node scripts/find-ip.js
```

**Update NotificationService.ts:**
```typescript
const DEV_IP = '***********'; // Your actual WiFi IP
```

**Requirements:**
- ✅ Phone and computer on same WiFi network
- ✅ Backend server running on your computer
- ✅ Most reliable for testing notifications

### **2. Android Emulator**

**Option A: Use special Android emulator IP**
```typescript
const DEV_IP = '********'; // Maps to host machine's localhost
```

**Option B: Use your computer's IP (recommended)**
```typescript
const DEV_IP = '***********'; // Your WiFi IP address
```

### **3. iOS Simulator**

**Use your computer's IP address:**
```typescript
const DEV_IP = '***********'; // Your WiFi IP address
```

**Note:** iOS Simulator shares the host network, so your IP address works best.

## 🔧 **Setup Steps**

### **Step 1: Find Your IP Address**

```bash
# Run this script to find your IP
node scripts/find-ip.js

# Output example:
# 📱 Available IP addresses:
# 1. en1: ***********
```

### **Step 2: Update NotificationService**

```typescript
// In app/services/NotificationService.ts
private getBackendUrl(): string {
  const DEV_IP = '***********'; // ← Update this with your IP
  const DEV_PORT = '3000';
  
  if (__DEV__) {
    return `http://${DEV_IP}:${DEV_PORT}`;
  } else {
    return 'https://your-production-server.com';
  }
}
```

### **Step 3: Start Backend Server**

```bash
cd backend-example
npm install
npm start

# Output will show:
# 📱 For React Native development, use these URLs:
#    Local IP: http://***********:3000/api/send-notification
```

### **Step 4: Test Connection**

```bash
# Test if your backend is reachable
curl http://***********:3000/health

# Should return:
# {"status":"OK","message":"Firebase Notification Server is running"}
```

## 🔍 **Troubleshooting**

### **Problem: Connection Refused**

```
Error: Network request failed
```

**Solutions:**
1. ✅ Check if backend server is running
2. ✅ Verify IP address is correct
3. ✅ Ensure phone/emulator is on same WiFi
4. ✅ Check firewall settings

### **Problem: Wrong IP Address**

```bash
# Get your current IP
ifconfig | grep "inet " | grep -v 127.0.0.1

# Or use our script
node scripts/find-ip.js
```

### **Problem: Android Emulator Issues**

**Try these IPs in order:**
1. `********` (Android emulator special IP)
2. Your WiFi IP (e.g., `***********`)
3. `********` (Alternative Android emulator IP)

### **Problem: iOS Simulator Issues**

**Use your WiFi IP address:**
```typescript
const DEV_IP = '***********'; // Not localhost or 127.0.0.1
```

## 📱 **Testing Different Scenarios**

### **Test 1: Backend Reachability**

```javascript
// Add this to your app for testing
const testBackendConnection = async () => {
  try {
    const response = await fetch('http://***********:3000/health');
    const data = await response.json();
    console.log('✅ Backend reachable:', data);
  } catch (error) {
    console.error('❌ Backend not reachable:', error);
  }
};
```

### **Test 2: Notification Sending**

```javascript
// Test notification endpoint
const testNotification = async () => {
  try {
    const response = await fetch('http://***********:3000/api/send-notification', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token: 'test-token',
        notification: {
          title: 'Test',
          body: 'Connection working!'
        }
      })
    });
    console.log('✅ Notification endpoint working');
  } catch (error) {
    console.error('❌ Notification endpoint failed:', error);
  }
};
```

## 🌐 **Production Deployment**

### **For Production Apps:**

```typescript
private getBackendUrl(): string {
  const DEV_IP = '***********';
  const DEV_PORT = '3000';
  const PROD_URL = 'https://your-api.herokuapp.com'; // Your deployed server
  
  return __DEV__ ? `http://${DEV_IP}:${DEV_PORT}` : PROD_URL;
}
```

### **Popular Deployment Options:**

1. **Heroku** - Easy deployment
2. **Vercel** - Serverless functions
3. **AWS Lambda** - Scalable serverless
4. **Google Cloud Run** - Container-based
5. **DigitalOcean** - VPS hosting

## 🎯 **Quick Reference**

| Scenario | IP Address | Example |
|----------|------------|---------|
| Physical Device | Your WiFi IP | `***********:3000` |
| Android Emulator | `********` or WiFi IP | `********:3000` |
| iOS Simulator | Your WiFi IP | `***********:3000` |
| Production | Your domain | `https://api.yourapp.com` |

## ✅ **Final Checklist**

- [ ] Found your computer's IP address
- [ ] Updated `DEV_IP` in NotificationService.ts
- [ ] Backend server is running
- [ ] Phone/emulator on same WiFi network
- [ ] Tested backend connection
- [ ] Firebase service account key configured
- [ ] CORS enabled on backend server

**Now your React Native app can send real system notifications!** 🔔✨
