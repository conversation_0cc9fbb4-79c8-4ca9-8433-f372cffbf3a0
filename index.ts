import { registerRootComponent } from 'expo';
import messaging from '@react-native-firebase/messaging';
import { Notifications } from 'react-native-notifications';
import { Platform } from 'react-native';

import App from './App';

// Register background handler for Firebase Cloud Messaging
// This MUST be done before registerRootComponent for system notifications to work
messaging().setBackgroundMessageHandler(async (remoteMessage) => {
  console.log('📬 Background FCM Message received:', remoteMessage);

  // Show system notification when app is in background/closed
  if (remoteMessage.notification) {
    const title = remoteMessage.notification.title || 'New Notification';
    const body = remoteMessage.notification.body || '';
    const notificationId = Date.now();

    console.log('🔔 Showing background system notification:', title, body);

    try {
      if (Platform.OS === 'android') {
        // Try to use native Android notification module for guaranteed system tray display
        const { NativeModules } = require('react-native');

        if (NativeModules.NotificationModule) {
          const notificationData = {
            id: notificationId,
            title: title,
            body: body,
            channelId: 'high-priority',
          };

          console.log('📱 Using native Android notification in background:', notificationData);
          await NativeModules.NotificationModule.showNotification(notificationData);
          console.log('✅ Native background notification sent successfully');
        } else {
          // Fallback to react-native-notifications
          const notificationPayload: any = {
            identifier: notificationId.toString(),
            title: title,
            body: body,
            sound: 'default',
            badge: 1,
            payload: remoteMessage.data || {},
            category: 'sunrise_notifications',
            android: {
              channelId: 'high-priority',
              priority: 'high',
              autoCancel: true,
              color: '#FF4081',
              vibrate: true,
              lights: true,
              ongoing: false,
            }
          };

          console.log('📱 Using react-native-notifications in background:', notificationPayload);
          Notifications.postLocalNotification(notificationPayload);
          console.log('✅ Background system notification posted successfully');
        }
      } else {
        // iOS notification
        const notificationPayload: any = {
          identifier: notificationId.toString(),
          title: title,
          body: body,
          sound: 'default',
          badge: 1,
          payload: remoteMessage.data || {},
          category: 'sunrise_notifications',
        };

        console.log('📱 Using iOS notification in background:', notificationPayload);
        Notifications.postLocalNotification(notificationPayload);
        console.log('✅ Background iOS notification posted successfully');
      }
    } catch (error) {
      console.error('❌ Error posting background notification:', error);
    }
  }
});

console.log('✅ Firebase background message handler registered');

registerRootComponent(App);
