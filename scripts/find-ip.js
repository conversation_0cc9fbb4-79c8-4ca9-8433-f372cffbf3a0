#!/usr/bin/env node

// <PERSON>ript to find your computer's IP address for React Native development

const os = require('os');

function getLocalIPAddress() {
  const interfaces = os.networkInterfaces();
  const addresses = [];

  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        addresses.push({
          name: name,
          address: interface.address
        });
      }
    }
  }

  return addresses;
}

function main() {
  console.log('🔍 Finding your computer\'s IP address for React Native development...\n');

  const addresses = getLocalIPAddress();

  if (addresses.length === 0) {
    console.log('❌ No external IP addresses found.');
    console.log('Make sure you\'re connected to a network (WiFi or Ethernet).\n');
    return;
  }

  console.log('📱 Available IP addresses:');
  addresses.forEach((addr, index) => {
    console.log(`${index + 1}. ${addr.name}: ${addr.address}`);
  });

  console.log('\n🔧 How to use:');
  console.log('1. Copy one of the IP addresses above (usually WiFi/Ethernet)');
  console.log('2. Update DEV_IP in app/services/NotificationService.ts');
  console.log('3. Make sure your backend server is running on the same network');
  console.log('4. Use this IP address instead of localhost\n');

  // Show example for the first address
  if (addresses.length > 0) {
    const primaryIP = addresses[0].address;
    console.log('📝 Example configuration:');
    console.log(`const DEV_IP = '${primaryIP}';`);
    console.log(`Backend URL: http://${primaryIP}:3000\n`);
  }

  console.log('💡 Tips:');
  console.log('- Make sure your phone/emulator is on the same WiFi network');
  console.log('- If using Android emulator, you might need to use ********');
  console.log('- If using iOS simulator, your computer\'s IP should work');
  console.log('- For physical devices, use your WiFi IP address');
}

if (require.main === module) {
  main();
}

module.exports = { getLocalIPAddress };
