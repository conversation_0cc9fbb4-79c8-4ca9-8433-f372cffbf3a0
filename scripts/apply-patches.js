const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Apply patches
console.log('Applying patches...');

try {
  // Create patches directory if it doesn't exist
  if (!fs.existsSync('patches')) {
    fs.mkdirSync('patches');
  }

  // Apply the patch
  execSync('npx patch-package', { stdio: 'inherit' });
  console.log('Patches applied successfully!');
} catch (error) {
  console.error('Error applying patches:', error);
  process.exit(1);
}
