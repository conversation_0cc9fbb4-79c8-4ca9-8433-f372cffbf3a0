diff --git a/node_modules/expo-modules-autolinking/scripts/android/autolinking_implementation.gradle b/node_modules/expo-modules-autolinking/scripts/android/autolinking_implementation.gradle
index 0000000..0000000 100644
--- a/node_modules/expo-modules-autolinking/scripts/android/autolinking_implementation.gradle
+++ b/node_modules/expo-modules-autolinking/scripts/android/autolinking_implementation.gradle
@@ -333,6 +333,11 @@
     gradle.afterProject { project ->
       if (!project.plugins.hasPlugin('com.android.application')) {
         return
+      }
+      // Skip applying plugins to the app project to avoid conflicts
+      // between 'com.android.application' and 'com.android.library'
+      if (project.name == 'app') {
+        return
       }
       for (module in modules) {
         for (modulePlugin in module.plugins) {
