diff --git a/node_modules/@react-native-firebase/app/android/build.gradle b/node_modules/@react-native-firebase/app/android/build.gradle
index 862ee1d..2d90a4c 100644
--- a/node_modules/@react-native-firebase/app/android/build.gradle
+++ b/node_modules/@react-native-firebase/app/android/build.gradle
@@ -1,4 +1,5 @@
-import io.invertase.gradle.common.PackageJson
+// Temporarily comment out the import to avoid build errors
+// import io.invertase.gradle.common.PackageJson
 import org.gradle.internal.jvm.Jvm
 
 buildscript {
@@ -9,6 +10,8 @@ buildscript {
     repositories {
       google()
       mavenCentral()
+      maven { url 'https://www.jitpack.io' }
+      gradlePluginPortal()
     }
 
     dependencies {
@@ -17,16 +20,18 @@ buildscript {
   }
 }
 
-plugins {
-  id "io.invertase.gradle.build" version "1.5"
-}
+// Temporarily comment out the plugin to avoid build errors
+// plugins {
+//   id "io.invertase.gradle.build" version "1.5"
+// }
 
-def packageJson = PackageJson.getForProject(project)
-def firebaseBomVersion = packageJson['sdkVersions']['android']['firebase']
-def playServicesAuthVersion = packageJson['sdkVersions']['android']['playServicesAuth']
-def jsonMinSdk = packageJson['sdkVersions']['android']['minSdk']
-def jsonTargetSdk = packageJson['sdkVersions']['android']['targetSdk']
-def jsonCompileSdk = packageJson['sdkVersions']['android']['compileSdk']
+// Temporarily hardcode these values to avoid build errors
+// def packageJson = PackageJson.getForProject(project)
+def firebaseBomVersion = "32.7.4" // Latest Firebase BOM version
+def playServicesAuthVersion = "21.0.0" // Latest Play Services Auth version
+def jsonMinSdk = 24
+def jsonTargetSdk = 34
+def jsonCompileSdk = 35
 
 project.ext {
   set('react-native', [
@@ -48,8 +53,16 @@ project.ext {
   ])
 }
 
+// Apply the firebase-json.gradle script
 apply from: file('./firebase-json.gradle')
 
+// Define the FIREBASE_JSON_RAW variable
+def firebaseJsonFile = new File(rootProject.projectDir.parentFile, 'firebase.json')
+def firebaseJsonString = "{}"
+if (firebaseJsonFile.exists()) {
+  firebaseJsonString = firebaseJsonFile.text
+}
+
 // If data collection isn't specifically disabled, default is enabled
 String dataCollectionDefaultEnabled = 'true'
 
@@ -59,13 +72,34 @@ if (rootProject.ext && rootProject.ext.firebaseJson) {
   }
 }
 
+// Apply the android plugin to enable Android-specific features
+apply plugin: 'com.android.library'
+
+// Now we can access the android property after applying the plugin
+android.buildTypes.each { buildType ->
+  // Properly escape the JSON string for Java
+  def escapedJson = firebaseJsonString
+    .replace('\\', '\\\\')  // Escape backslashes first
+    .replace('"', '\\"')    // Escape double quotes
+    .replace('\n', '\\n')   // Escape newlines
+    .replace('\t', '\\t')   // Escape tabs
+
+  buildType.buildConfigField "String", "FIREBASE_JSON_RAW", "\"${escapedJson}\""
+}
+
 android {
   def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION.tokenize('.')[0].toInteger()
   if (agpVersion >= 7) {
     namespace = 'io.invertase.firebase'
   }
 
+  // Use the compileSdkVersion from the root project
+  compileSdkVersion rootProject.ext.compileSdkVersion
+  buildToolsVersion rootProject.ext.buildToolsVersion
+
   defaultConfig {
+    minSdkVersion rootProject.ext.minSdkVersion
+    targetSdkVersion rootProject.ext.targetSdkVersion
     multiDexEnabled = true
     manifestPlaceholders = [
       firebaseJsonDataCollectionDefaultEnabled: dataCollectionDefaultEnabled
@@ -81,11 +115,10 @@ android {
     disable 'GradleCompatible'
     abortOnError = false
   }
-  if (agpVersion < 8) {
-    compileOptions {
-      sourceCompatibility = JavaVersion.VERSION_11
-      targetCompatibility = JavaVersion.VERSION_11
-    }
+
+  compileOptions {
+    sourceCompatibility = JavaVersion.VERSION_11
+    targetCompatibility = JavaVersion.VERSION_11
   }
 
   sourceSets {
@@ -101,9 +134,12 @@ repositories {
 }
 
 dependencies {
-  implementation platform("com.google.firebase:firebase-bom:${ReactNative.ext.getVersion("firebase", "bom")}")
+  implementation platform("com.google.firebase:firebase-bom:${firebaseBomVersion}")
   implementation "com.google.firebase:firebase-common"
-  implementation "com.google.android.gms:play-services-auth:${ReactNative.ext.getVersion("play", "play-services-auth")}"
+  implementation "com.google.android.gms:play-services-auth:${playServicesAuthVersion}"
+
+  // Add React Native dependency
+  implementation "com.facebook.react:react-android"
 }
 
 def jvmVersion = Jvm.current().javaVersion?.majorVersion
@@ -122,7 +158,8 @@ if ((jvmVersion?.toInteger() ?: 17) < 17) {
   System.exit(1)
 }
 
-ReactNative.shared.applyPackageVersion()
-ReactNative.shared.applyDefaultExcludes()
-ReactNative.module.applyAndroidVersions()
-ReactNative.module.applyReactNativeDependency("api")
+// Temporarily comment out these calls to avoid build errors
+// ReactNative.shared.applyPackageVersion()
+// ReactNative.shared.applyDefaultExcludes()
+// ReactNative.module.applyAndroidVersions()
+// ReactNative.module.applyReactNativeDependency("api")
diff --git a/node_modules/@react-native-firebase/app/android/build/generated/source/buildConfig/debug/io/invertase/firebase/BuildConfig.java b/node_modules/@react-native-firebase/app/android/build/generated/source/buildConfig/debug/io/invertase/firebase/BuildConfig.java
new file mode 100644
index 0000000..855f6ae
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/generated/source/buildConfig/debug/io/invertase/firebase/BuildConfig.java
@@ -0,0 +1,22 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package io.invertase.firebase;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "io.invertase.firebase";
+  public static final String BUILD_TYPE = "debug";
+  // Field from build type: debug
+  public static final String FIREBASE_JSON_RAW = "{
+        \"react-native\": {
+          \"messaging_android_notification_channel_id\": \"high-priority\",
+          \"messaging_android_notification_color\": \"@color/colorAccent\",
+          \"messaging_android_notification_delivery_metrics_export_enabled\": true,
+          \"messaging_android_notification_delegation_enabled\": true,
+          \"messaging_auto_init_enabled\": true,
+          \"app_data_collection_default_enabled\": true
+        }
+      }
+      ";
+}
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml b/node_modules/@react-native-firebase/app/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..93b68de
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
@@ -0,0 +1,30 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="io.invertase.firebase" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <application>
+        <meta-data
+            android:name="app_data_collection_default_enabled"
+            android:value="true" />
+
+        <service
+            android:name="com.google.firebase.components.ComponentDiscoveryService"
+            android:directBootAware="true"
+            android:exported="false"
+            tools:targetApi="n" >
+            <meta-data
+                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
+                android:value="com.google.firebase.components.ComponentRegistrar" />
+        </service>
+
+        <provider
+            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
+            android:authorities="dollar_openBracket_applicationId_closeBracket.reactnativefirebaseappinitprovider"
+            android:exported="false"
+            android:initOrder="99" /> <!-- Firebase = 100, using 99 to run after Firebase initialises (highest first) -->
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json b/node_modules/@react-native-firebase/app/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
new file mode 100644
index 0000000..635500a
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "io.invertase.firebase",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties b/node_modules/@react-native-firebase/app/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json b/node_modules/@react-native-firebase/app/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar b/node_modules/@react-native-firebase/app/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar
new file mode 100644
index 0000000..65b2fea
Binary files /dev/null and b/node_modules/@react-native-firebase/app/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar differ
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt b/node_modules/@react-native-firebase/app/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/@react-native-firebase/app/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..dacca1c
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Fri May 23 12:18:05 IST 2025
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/@react-native-firebase/app/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..485187b
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt b/node_modules/@react-native-firebase/app/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt b/node_modules/@react-native-firebase/app/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..81f7846
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,46 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    xmlns:tools="http://schemas.android.com/tools"
+4    package="io.invertase.firebase" >
+5
+6    <uses-sdk android:minSdkVersion="24" />
+7
+8    <application>
+8-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:6:3-23:17
+9        <meta-data
+10            android:name="app_data_collection_default_enabled"
+10-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:7:16-66
+11            android:value="true" />
+11-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:7:67-126
+12
+13        <service
+13-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:8:5-17:15
+14            android:name="com.google.firebase.components.ComponentDiscoveryService"
+14-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:9:7-78
+15            android:directBootAware="true"
+15-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:10:7-37
+16            android:exported="false"
+16-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:11:7-31
+17            tools:targetApi="n" >
+17-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:12:7-26
+18            <meta-data
+18-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:14:7-16:77
+19                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
+19-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:15:9-112
+20                android:value="com.google.firebase.components.ComponentRegistrar" />
+20-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:16:9-74
+21        </service>
+22
+23        <provider
+23-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:18:5-22:32
+24            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
+24-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:19:7-82
+25            android:authorities="${applicationId}.reactnativefirebaseappinitprovider"
+25-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:20:7-80
+26            android:exported="false"
+26-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:21:7-31
+27            android:initOrder="99" /> <!-- Firebase = 100, using 99 to run after Firebase initialises (highest first) -->
+27-->/Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:22:7-29
+28    </application>
+29
+30</manifest>
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml b/node_modules/@react-native-firebase/app/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
new file mode 100644
index 0000000..d6fbb89
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
@@ -0,0 +1,30 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="io.invertase.firebase" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <application>
+        <meta-data
+            android:name="app_data_collection_default_enabled"
+            android:value="true" />
+
+        <service
+            android:name="com.google.firebase.components.ComponentDiscoveryService"
+            android:directBootAware="true"
+            android:exported="false"
+            tools:targetApi="n" >
+            <meta-data
+                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
+                android:value="com.google.firebase.components.ComponentRegistrar" />
+        </service>
+
+        <provider
+            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
+            android:authorities="${applicationId}.reactnativefirebaseappinitprovider"
+            android:exported="false"
+            android:initOrder="99" /> <!-- Firebase = 100, using 99 to run after Firebase initialises (highest first) -->
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json b/node_modules/@react-native-firebase/app/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt b/node_modules/@react-native-firebase/app/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
new file mode 100644
index 0000000..08f4ebe
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
@@ -0,0 +1 @@
+0 Warning/Error
\ No newline at end of file
diff --git a/node_modules/@react-native-firebase/app/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt b/node_modules/@react-native-firebase/app/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
new file mode 100644
index 0000000..b22a75e
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
@@ -0,0 +1 @@
+io.invertase.firebase
diff --git a/node_modules/@react-native-firebase/app/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/@react-native-firebase/app/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..b5eda5f
--- /dev/null
+++ b/node_modules/@react-native-firebase/app/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,54 @@
+-- Merging decision tree log ---
+meta-data#app_data_collection_default_enabled
+INJECTED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:7:5-128
+	android:value
+		INJECTED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:7:67-126
+	android:name
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:7:16-66
+manifest
+ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:2:1-24:12
+INJECTED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:2:1-24:12
+	package
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:4:3-34
+		INJECTED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml
+	xmlns:tools
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:3:3-49
+	xmlns:android
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:2:11-69
+application
+ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:6:3-23:17
+service#com.google.firebase.components.ComponentDiscoveryService
+ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:8:5-17:15
+	android:exported
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:11:7-31
+	tools:targetApi
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:12:7-26
+	android:directBootAware
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:10:7-37
+	android:name
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:9:7-78
+meta-data#com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar
+ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:14:7-16:77
+	android:value
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:16:9-74
+	android:name
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:15:9-112
+provider#io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider
+ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:18:5-22:32
+	android:authorities
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:20:7-80
+	android:exported
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:21:7-31
+	android:initOrder
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:22:7-29
+	android:name
+		ADDED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml:19:7-82
+uses-sdk
+INJECTED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/Documents/GitHub/SunriseB2B-App/node_modules/@react-native-firebase/app/android/src/main/AndroidManifest.xml
diff --git a/node_modules/@react-native-firebase/app/android/firebase-json.gradle b/node_modules/@react-native-firebase/app/android/firebase-json.gradle
index 73593aa..cead377 100644
--- a/node_modules/@react-native-firebase/app/android/firebase-json.gradle
+++ b/node_modules/@react-native-firebase/app/android/firebase-json.gradle
@@ -48,26 +48,29 @@ if (jsonFile?.exists()) {
 
     rootProject.logger
       .info ":${project.name} found react-native json root in firebase.json, creating firebase build config"
-    android {
-      defaultConfig {
-        buildConfigField 'String', 'FIREBASE_JSON_RAW', jsonStr
-      }
-    }
+    // Temporarily comment out the android block to avoid build errors
+    // android {
+    //   defaultConfig {
+    //     buildConfigField 'String', 'FIREBASE_JSON_RAW', jsonStr
+    //   }
+    // }
   } else {
     rootProject.ext.firebaseJson = false
     rootProject.logger.info ":${project.name} firebase.json found with no react-native config, skipping"
-    android {
-      defaultConfig {
-        buildConfigField 'String', 'FIREBASE_JSON_RAW', '"{}"'
-      }
-    }
+    // Temporarily comment out the android block to avoid build errors
+    // android {
+    //   defaultConfig {
+    //     buildConfigField 'String', 'FIREBASE_JSON_RAW', '"{}"'
+    //   }
+    // }
   }
 } else {
   rootProject.ext.firebaseJson = false
   rootProject.logger.info ":${project.name} no firebase.json found, skipping"
-  android {
-    defaultConfig {
-      buildConfigField 'String', 'FIREBASE_JSON_RAW', '"{}"'
-    }
-  }
+  // Temporarily comment out the android block to avoid build errors
+  // android {
+  //   defaultConfig {
+  //     buildConfigField 'String', 'FIREBASE_JSON_RAW', '"{}"'
+  //   }
+  // }
 }
